{
  "Version": "5.9.0", // 平台版本号
  "Title": "PureAdmin", // 平台标题
  "FixedHeader": true, // 是否固定页头和标签页（true 内容区超出出现纵向滚动条 false 页头、标签页、内容区可纵向滚动）
  "HiddenSideBar": false, // 隐藏菜单和页头，只显示标签页和内容区
  "MultiTagsCache": false, // 是否开启持久化标签 （会缓存）
  "KeepAlive": true, // 是否开启组件缓存（此处不同于路由的 keepAlive，如果此处为 true 表示设置路由的 keepAlive 起效，反之设置 false 屏蔽平台整体的 keepAlive，即使路由设置了keepAlive 也不再起作用）
  "Locale": "zh", // 默认国际化语言 （zh 中文、en 英文）（会缓存）（max版本额外配置：tw 繁體中文、ja 日语、ko 韩语）
  "Layout": "vertical", // 导航菜单模式 （vertical 左侧菜单模式、horizontal 顶部菜单模式、mix 混合菜单模式）（会缓存）（max版本额外配置：double 左侧双栏菜单模式）
  "Theme": "light", // 主题模式（会缓存）
  "DarkMode": false, // 是否开启暗黑模式 （会缓存）
  "OverallStyle": "light", // 整体风格（浅色：light、深色：dark、自动：system）（会缓存）更多详情看 https://github.com/pure-admin/vue-pure-admin/commit/dd783136229da9e291b518df93227111f4216ad0#commitcomment-137027417
  "Grey": false, // 灰色模式（会缓存）
  "Weak": false, // 色弱模式（会缓存）
  "HideTabs": false, // 是否隐藏标签页（会缓存）
  "HideFooter": false, // 是否隐藏页脚（会缓存）
  "SidebarStatus": true, // vertical左侧菜单模式模式下侧边栏状态（true 展开、false 收起）（会缓存）
  "EpThemeColor": "#409EFF", // 主题色（会缓存）
  "ShowLogo": true, // 是否显示logo（会缓存）
  "ShowModel": "smart", // 标签页风格（smart 灵动模式、card 卡片模式）（会缓存）
  "MenuArrowIconNoTransition": false, // 菜单展开、收起图标是否开启动画，如遇菜单展开、收起卡顿设置成 true 即可（默认 false，开启动画）
  "CachingAsyncRoutes": false, // 是否开启动态路由缓存本地的全局配置，默认 false
  "TooltipEffect": "light", // 可配置平台主体所有 el-tooltip 的 effect 属性，默认 light，不会影响业务代码
  "ResponsiveStorageNameSpace": "responsive-", // 本地响应式存储的命名空间
  "MenuSearchHistory": 6 // 菜单搜索历史的最大条目
}

当你需要在一个异步流程中，立即返回一个成功的结果，而不需要等待任何异步操作时，Promise.resolve() 非常方便。这句话的意思是说，在某些情况下，你正在编写的代码是设计用来处理异步操作的（比如网络请求、文件读取、定时器等等），
但在特定的执行路径下，你实际上并不需要执行任何耗时的异步操作，你已经可以直接得到一个成功的结果了。
这时候，Promise.resolve() 就派上用场(TS里不直接返回结果是为了让结果类型变成Promise类型让结果和异步返回结果类型一样，能写在一个方法里)，让你能够立即返回一个表示“成功”的 Promise，而无需模拟异步等待或者使用更复杂的 Promise 创建方式。


一、
<script setup lang="ts">
import { getCurrentInstance } from "vue";

const { Version } = getCurrentInstance().appContext.config.globalProperties.$config;
console.log("Version：", Version)
</script>

二、
import { getConfig } from "@/config";

console.log("Version：", getConfig()?.Version)
