If requirements or instructions are unclear use the tool interactive_feedback to ask clarifying questions to the user before proceeding, do not make assumptions. Whenever possible, present the user with predefined options through the interactive_feedback MCP tool to facilitate quick decisions.

Whenever you're about to complete a user request, call the interactive_feedback tool to request user feedback before ending the process. If the feedback is empty you can end the request and don't call the tool in loop.

const vue3CompositionApiBestPractices = [
  "Use setup() function for component logic",
  "Utilize ref and reactive for reactive state",
  "Implement computed properties with computed()",
  "Use watch and watchEffect for side effects",
  "Implement lifecycle hooks with onMounted, onUpdated, etc.",
  "Utilize provide/inject for dependency injection",
];

// Folder structure

const folderStructure = `
src/
  components/
  composables/
  views/
  router/
  store/
  assets/
  App.vue
  main.js
`;

// Additional instructions

const additionalInstructions = `
1. Use TypeScript for type safety
2. Implement proper props and emits definitions
3. Utilize Vue 3's Teleport component when needed
4. Use Suspense for async components
5. Implement proper error handling
6. Follow Vue 3 style guide and naming conventions
7. Use Vite for fast development and building
`;

