@echo off
echo ========================================
echo 🚀 自动导入数据库部署脚本
echo ========================================

echo.
echo 📋 检查必要文件...
if not exist "docker-compose.yml" (
    echo ❌ 错误: docker-compose.yml 文件不存在
    pause
    exit /b 1
)

if not exist "db\pure.sql" (
    echo ❌ 错误: db\pure.sql 文件不存在
    pause
    exit /b 1
)

echo ✅ 文件检查完成

echo.
echo 🛑 停止现有容器...
docker-compose down

echo.
echo 🗑️ 清理数据库卷 (重要: 这将删除现有数据)
set /p confirm="确认删除现有数据库数据? (y/N): "
if /i "%confirm%"=="y" (
    docker volume rm myapp_db-data 2>nul
    echo ✅ 数据库卷已清理
) else (
    echo ⚠️ 跳过数据库卷清理 - 可能不会导入新数据
)

echo.
echo 🔧 检查环境变量...
if not exist ".env" (
    echo 📝 创建 .env 文件...
    echo MYSQL_ROOT_PASSWORD=your_password_here > .env
    echo ⚠️ 请编辑 .env 文件设置正确的密码
    pause
)

echo.
echo 🚀 启动服务 (数据库将自动导入)...
docker-compose up -d db

echo.
echo ⏳ 等待数据库启动和数据导入...
timeout /t 30 /nobreak

echo.
echo 📊 检查数据库状态...
docker-compose logs db

echo.
echo 🎉 部署完成！
echo.
echo 📝 下一步操作:
echo 1. 检查上方日志确认数据导入成功
echo 2. 运行: docker-compose up -d 启动所有服务
echo 3. 访问: http://localhost:9000 (Portainer管理界面)
echo.
pause
