# 使用 Node.js 22 LTS 完整版镜像进行构建（包含完整系统依赖）
FROM node:22

# 设置工作目录
WORKDIR /app

# 安装 pnpm 和必要的工具
RUN npm install -g pnpm@latest

# 设置 pnpm 配置
RUN pnpm config set registry https://registry.npmmirror.com/

# 复制 package.json 和 pnpm-lock.yaml (利用Docker缓存层)
COPY package.json pnpm-lock.yaml ./

# 清理并重新安装依赖
RUN pnpm install --frozen-lockfile --prefer-offline

# 手动安装缺失的依赖包
RUN pnpm add vue-demi tippy.js @element-plus/icons-vue

# 复制源代码
COPY . .

# 设置环境变量（使用与本地一致的配置）
ENV NODE_ENV=production

# 清理缓存并重新构建
RUN pnpm store prune && pnpm build

# 创建启动脚本，将构建文件复制到挂载的volume并退出
RUN echo '#!/bin/sh' > /entrypoint.sh && \
    echo 'echo "开始复制构建文件到nginx volume..."' >> /entrypoint.sh && \
    echo 'mkdir -p /var/www/html' >> /entrypoint.sh && \
    echo 'cp -r /app/dist/* /var/www/html/' >> /entrypoint.sh && \
    echo 'echo "构建文件复制完成！"' >> /entrypoint.sh && \
    echo 'ls -la /var/www/html' >> /entrypoint.sh && \
    echo 'echo "前端构建容器任务完成，退出..."' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

# 执行复制并退出
CMD ["/entrypoint.sh"]
