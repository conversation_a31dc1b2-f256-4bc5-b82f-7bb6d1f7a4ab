import { defineStore } from "pinia";
import { computed, ref } from "vue";

export const useVideoStore = defineStore("video", () => {
  const progress = ref<Record<string | number, number>>({});

  // Getters (使用 computed)
  /**
   * 获取指定 videoId 的播放进度
   * @param {string} videoId - 视频的唯一标识符
   * @returns {import('vue').ComputedRef<number>} - 返回一个计算属性，值为进度或0
   */
  function getProgressByVideoId(videoId: string | number) {
    /* 当computed里面的条件(progress.value[videoId]里的progress.value或者videoId)
      发生变化时,任何使用return返回的结果的值也会实时更新为拿新数据计算的结果
      如果不用computed，就需要再拿最新的videoId执行一次getProgressByVideoId（videoId）实现更新
     */
    return computed(() => progress.value[videoId] || 0);
  };

  /*
   * 更新指定 videoId 的播放进度
   * @param {string} videoId - 视频的唯一标识符
   * @param {number} time - 当前播放时间（秒）
   */
  function updateProgress(videoId: string | number, time: number) {
    // 直接修改 ref 的 .value
    progress.value = {
      ...progress.value,
      [videoId]: time
    };
  }

  /**
   * 清除指定 videoId 的播放进度
   * @param {string} videoId - 视频的唯一标识符
   */
  function clearVideoProgress(videoId: string | number) {
    const newProgress = { ...progress.value };
    delete newProgress[videoId];
    progress.value = newProgress;
  }

  return {
    progress, // 暴露 state (ref)
    getProgressByVideoId, // 暴露 getter (函数返回 computed)
    updateProgress, // 暴露 action
    clearVideoProgress // 暴露 action
  }
}, { // <-- options object starts here
  persist: {
    key: 'xgplayer_video_progress', // 持久化键名
    pick: ['progress'], // 只持久化这个变量
  }
}); // <-- defineStore call ends here
