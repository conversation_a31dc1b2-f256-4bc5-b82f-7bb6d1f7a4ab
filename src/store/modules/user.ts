import { defineStore } from "pinia"
import { ref } from "vue"
import {
  resetRouter,
  router,
  routerArrays,
  storageLocal,
  store
} from "../utils"
import {
  getLogin,
  refreshTokenApi,
  getUserProfile,
  type GetTokenResult
} from "@/api/user"
import { useMultiTagsStoreHook } from "./multiTags"
import { type DataInfo, removeToken, setToken, userKey } from "@/utils/auth"

export const useUserStore = defineStore(
  "pure-user",
  () => {
    // State
    // 头像
    const avatar = ref(
      storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? ""
    )
    // 用户名
    const username = ref(
      storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? ""
    )
    // 昵称
    const nickname = ref(
      storageLocal().getItem<DataInfo<number>>(userKey)?.nickname ?? ""
    )
    // 页面级别权限
    const roles = ref<Array<string>>(
      storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? []
    )
    // 按钮级别权限
    const permissions = ref<Array<string>>(
      storageLocal().getItem<DataInfo<number>>(userKey)?.permissions ?? []
    )
    // 前端生成的验证码（按实际需求替换）
    const verifyCode = ref("")
    // 判断登录页面显示哪个组件（0：登录（默认）、1：手机登录、2：二维码登录、3：注册、4：忘记密码）
    const currentPage = ref(0)
    const accessToken = ref("")
    // 是否勾选了登录页的免登录
    const isRemembered = ref(false)
    // 登录页的免登录存储几天，默认7天
    const loginDay = ref(7)
    const email = ref("")
    const phone = ref("")
    const description = ref("")

    /** 存储头像 */
    function SET_AVATAR(value: string) {
      avatar.value = value
    }

    /** 存储用户名 */
    function SET_USERNAME(value: string) {
      username.value = value
    }

    /** 存储昵称 */
    function SET_NICKNAME(value: string) {
      nickname.value = value
    }

    /** 存储角色 */
    function SET_ROLES(value: Array<string>) {
      roles.value = value
    }

    /** 存储按钮级别权限 */
    function SET_PERMS(value: Array<string>) {
      permissions.value = value
    }

    /** 存储前端生成的验证码 */
    function SET_VERIFYCODE(value: string) {
      verifyCode.value = value
    }

    /** 存储登录页面显示哪个组件 */
    function SET_CURRENTPAGE(value: number) {
      currentPage.value = value
    }

    /** 存储是否勾选了登录页的免登录 */
    function SET_ISREMEMBERED(value: boolean) {
      isRemembered.value = value
    }

    /** 设置登录页的免登录存储几天 */
    function SET_LOGINDAY(value: number) {
      loginDay.value = Number(value)
    }

    function SetEmail(value: string) {
      email.value = value
    }

    function SetPhone(value: string) {
      phone.value = value
    }

    function SetAccessToken(value: string) {
      accessToken.value = value
    }

    /** 登入 */
    async function loginByUsername(data: any) {
      return new Promise<GetTokenResult>((resolve, reject) => {
        getLogin(data)
          .then(res => {
            if (res?.success) setToken(res.data)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    }

    /** 前端登出（不调用接口） */
    function logOut() {
      username.value = ""
      roles.value = []
      permissions.value = []
      removeToken()
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays])

      resetRouter()
      router.push("/login")
    }

    /** 刷新`token` */
    async function handRefreshToken() {
      return new Promise<GetTokenResult>((resolve, reject) => {
        refreshTokenApi()
          .then(data => {
            if (data && data.success) {
              setToken(data.data)
              resolve(data)
            }
          })
          .catch(error => {
            reject(error)
          })
      })
    }

    /** 从API获取并设置用户资料 */
    async function fetchAndSetUserProfile() {
      try {
        const res = await getUserProfile()
        if (res.success && res.data) {
          const userProfileData = res.data

          // 使用setToken持久化最新的配置文件数据
          //setToken(userProfileData)

          // 更新Pinia状态
          avatar.value = userProfileData.avatar
          username.value = userProfileData.username
          nickname.value = userProfileData.nickname
          roles.value = userProfileData.roles
          permissions.value = userProfileData.permissions
          email.value = userProfileData.email
          phone.value = userProfileData.phone
          description.value = userProfileData.description
        } else {
          console.error("无法从API获取用户资料或数据缺失:", res)
        }
      } catch (error) {
        console.error("从API获取用户资料时出错:", error)
      }
    }

    return {
      avatar,
      username,
      nickname,
      roles,
      permissions,
      verifyCode,
      currentPage,
      accessToken,
      isRemembered,
      loginDay,
      email,
      phone,
      description,
      SET_AVATAR,
      SET_USERNAME,
      SET_NICKNAME,
      SET_ROLES,
      SET_PERMS,
      SET_VERIFYCODE,
      SET_CURRENTPAGE,
      SET_ISREMEMBERED,
      SET_LOGINDAY,
      SetEmail,
      SetPhone,
      loginByUsername,
      logOut,
      handRefreshToken,
      fetchAndSetUserProfile,
      SetAccessToken
    }
  },
  {
    // <-- options object starts here
    persist: {
      key: "user_info" // 持久化键名
      // paths不写就会初始化全部数据
    }
  }
)

export function useUserStoreHook() {
  return useUserStore(store)
}
