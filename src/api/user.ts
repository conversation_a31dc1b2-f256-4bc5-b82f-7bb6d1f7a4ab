import { http } from "@/utils/http"

export interface UserResult {
  success: boolean
  data: {
    /** 头像 */
    avatar: string
    /** 用户名 */
    username: string
    /** 昵称 */
    nickname: string
    /** 邮箱 */
    email: string
    /** 联系电话 */
    phone: string
    /** 简介 */
    description: string
    /** 当前登录用户的角色 */
    roles: Array<string>
    /** 按钮级别权限 */
    permissions?: Array<string>
    /** `token` */
    accessToken: string
    /** 联系电话 */
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string
    /** `accessToken`的过期时间（ISO 8601 字符串） */
    accessTokenExpiresIn?: string
  }
}

export interface GetTokenResult {
  success: boolean
  data: {
    /** 用户名 */
    username: string
    /** `token` */
    accessToken: string
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string
    /** `accessToken`的过期时间（ISO 8601 字符串） */
    accessTokenExpiresIn: string
  }
}

export interface UserInfo {
  /** 头像 */
  avatar: string
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 邮箱 */
  email: string
  /** 联系电话 */
  phone: string
  /** 简介 */
  description: string
}

export interface UserInfoResult {
  success: boolean
  data: UserInfo
}

interface ResultTable {
  success: boolean
  code: number
  message: string
  data?: {
    list?: []
    pageResult: {
      total: number
      pageNum: number
      pageSize: number
      pages: number
    }
  }

  time?: string
}

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<GetTokenResult>("post", "/api/auth/login", { data })
}

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<GetTokenResult>("post", "/api/auth/refresh", {
    data
  })
}

/** 账户设置-个人信息 */
export const getMine = () => {
  return http.request<UserInfoResult>("get", "/api/user-profile")
}

/** 账户设置-个人安全日志 */
export const getMineLogs = (data?: object) => {
  console.log("值为", data)
  return http.request<ResultTable>("get", "/api/logs/my-operations", {
    params: data
  })
}

/** 用户注册 */
export const register = (data?: object) => {
  return http.request<UserResult>("post", "/api/user/createUser", { data })
}

/** 发送邮箱验证码 */
export const sendEmailCode = (email: string) => {
  return http.request("post", "/api/verification/send-email-code", {
    params: { email }
  })
}

// 忘记密码或修改密码
export const updatePassword = (data?: object) => {
  return http.request("put", "/api/user/password", { data })
}

// 账户设置-更新详细个人信息
export const updateUserInfo = (data?: object) => {
  return http.request("put", "/api/user-profile", { data })
}

// 获取用户全部资料
export const getUserProfile = () => {
  return http.request<UserResult>("get", "/api/user/UserWithUserProfile")
}

/** 跳转到第三方授权页面 */
export const oauthRender = (source: string) => {
  // 使用环境变量获取后端地址，支持开发和生产环境自动切换
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:8080"
  const oauthUrl = `${apiBaseUrl}/oauth/render/${source}`
  // 直接跳转
  window.location.href = oauthUrl
}

