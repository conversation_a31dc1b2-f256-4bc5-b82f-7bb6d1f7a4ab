import { http } from "@/utils/http"

export interface MessageCount {
  message: string
  success: boolean
  data: any
  time: string
}

// 获取用户未读消息数量
export const getMessageCountApi = () => {
  return http.request<MessageCount>("get", "/api/messages/users/unread-count")
}

// 获取用户未读消息
export const getMessageApi = () => {
  return http.request<MessageCount>("get", "/api/messages/unread")
}

// 标记所有消息为已读消息
export const getMessageMarkAllAsReadApi = () => {
  return http.request<MessageCount>(
    "post",
    "/api/messages/users/mark-all-as-read"
  )
}

// 清空已读消息
export const deleteAllReadMessageApi = () => {
  return http.request<MessageCount>("delete", "/api/messages/delete/all")
}

// 删除单个消息
export const deleteMessageApi = (id: number) => {
  return http.request<MessageCount>("delete", `/api/messages/delete/${id}`)
}
