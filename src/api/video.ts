import { http } from "@/utils/http"

interface ApiResponse<T> {
  success: boolean
  code: number
  message: string
  data?: T
}

export interface VideoInfo {
  id: number
  title: string
  description: string
  coverImageUrl: string
}

export interface VideoEpisodes {
  id: number
  number: string
  playUrl: string
  likesCount: number
  status: boolean
  videoInfoId: number
  spriteSheetUrl: string[]
}

export interface VideoType {
  id: number
  name: string
}

export interface Params {
  keyword: string
  pageNum: number
  pageSize: number
}

export interface VideoDetailData {
  coverImageUrl: string
  id: number
  title: string
  description: string
  videoEpisodes: VideoEpisodes[]
  videoTypes: VideoType[]
  likes?: number
}

interface VideoInfoWithEpisodesWithType
  extends ApiResponse<{
    list: VideoInfo[]
    pageResult?: {
      total: number
      pageSize: number
      pageNum: number
      pages: number
    }
  }> {}

interface SingleVideoInfoWithEpisodesResult
  extends ApiResponse<VideoDetailData> {}

interface VideoAllType
  extends ApiResponse<{
    list: VideoInfo[]
    pageResult?: {}
  }> {}

/** 获取单个视频信息和视频的所有集数通过标题 */
export const getVideoInfoWithEpisodesApi = (title: string) => {
  return http.request<SingleVideoInfoWithEpisodesResult>(
    "get",
    "/api/videoUrl/videoinfo-with-episodes",
    {
      params: {
        title
      }
    }
  )
}

/**获取分页视频信息，如果有keyword则搜索，如果没有值则按分页获取视频信息 */
export const getVideoInfoWithPageApi = (data: Params) => {
  return http.request<VideoInfoWithEpisodesWithType>(
    "get",
    "/api/videoUrl/videoinfo/pagination",
    {
      params: data
    }
  )
}

/** 获取视频信息通过类型搜索获取按分页获取 */
export const getVideoInfoWithTypeApi = (data: Params) => {
  return http.request<VideoInfoWithEpisodesWithType>(
    "get",
    "/api/videoUrl/videoinfo/by-type/pagination",
    {
      params: data
    }
  )
}

// 获取视频所有分类
export const getAllVideoTypeApi = () => {
  return http.request<VideoAllType>("get", "/api/videoUrl/type")
}
