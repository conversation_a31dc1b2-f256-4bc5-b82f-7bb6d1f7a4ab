import { http } from "@/utils/http"

interface PageParams {
  videoEpisodesId: number
  pageNum: number
  pageSize: number
}


interface VideoCommentsList {
  list: VideoComments[]
  pageResult: PageResult
}

interface VideoComments {
  id: number
  videoEpisodesId: number
  content: string
  createTime: string
  userId: number
  parentCommentId: number
  username: string
  avatar: string
  likesCount: number
  dislikesCount: number
  userLikeType:string
  likeStatus: boolean
}

interface PageResult {
  total: number
  pageSize: number
  pageNum: number
  pages: number
}
interface CommentListResult<T> {
  success: boolean
  code: number
  message: string
  data?: T
}

interface CommentLikesRequest {
  commentId: number
  type: "like" | "dislike"
}

interface CommentLikeInfoResult {
  likeCount: number
  dislikeCount: number
  userLikeType: string // 当前登录用户的点赞状态，like和dislike或者为null
  likeStatus: boolean // true为已点赞或踩,null为从未进行过在数据库不创建表
}

/** 获取视频评论列表 */
export const getCommentListApi = (data: PageParams) => {
  return http.request<CommentListResult<VideoCommentsList>>(
    "get",
    "/api/video/comments",
    {
      params: data
    }
  )
}

/** 发送评论 */
export const sendCommentApi = (data: {
  content: string
  videoEpisodesId: number
  parentCommentId: number | null
}) => {
  return http.request<CommentListResult<null>>("post", "/api/video/comments", {
    data
  })
}

/** 删除评论 */
export const deleteCommentApi = (commentId: number) => {
  return http.request<CommentListResult<null>>(
    "delete",
    `/api/video/comments/${commentId}`
  )
}

// 点赞/踩评论
export const createVideoEpisodeCommentLikesApi = (
  data: CommentLikesRequest
) => {
  return http.request<CommentListResult<null>>(
    "post",
    "/api/video/comments/like",
    {
      data
    }
  )
}

// 获取单个评论的点赞/踩信息,用于评论点赞后更新点赞数量
export const getVideoEpisodeCommentLikesApi = (commentId: number) => {
  return http.request<CommentListResult<CommentLikeInfoResult>>(
    "get",
    `/api/video/comments/${commentId}/likes`
  )
}
