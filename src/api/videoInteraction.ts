import { http } from "@/utils/http"

interface LikeInfo {
  likesCount: number
  status: boolean
}


interface CommentListResult<T> {
  success: boolean
  code: number
  message: string
  data?: T
}


// 创建视频按集数点赞,如果已经点赞,则取消点赞
export const createVideoEpisodeLikeApi = (episodeId: number) => {
  return http.request<CommentListResult<null>>(
    "post",
    `/api/video/interaction/episode/${episodeId}/likes`
  )
}

// 视频集数点赞后请求这个API更新最新点赞数
export const getVideoEpisodeLikeApi = (episodeId: number) => {
  return http.request<CommentListResult<LikeInfo>>(
    "get",
    `/api/video/interaction/episode/${episodeId}/like-info`
  )
}

