import { http } from "@/utils/http"

type Result = {
  success: boolean
  data: Array<any>
}

export const getAsyncRoutes = () => {
  console.log("正在获取异步路由...")
  return http
    .request<Result>("get", "/get-async-routes")
    .then(res => {
      console.log("获取异步路由成功:", res)
      if (!res.data || !Array.isArray(res.data)) {
        console.warn("获取到的路由数据不是数组格式:", res.data)
        // 确保返回一个空数组而不是undefined
        res.data = []
      }
      return res
    })
    .catch(error => {
      console.error("获取异步路由失败:", error)
      // 返回一个模拟的成功响应，但包含空数组
      return {
        success: true,
        data: []
      }
    })
}
