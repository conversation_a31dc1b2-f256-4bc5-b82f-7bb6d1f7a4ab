import axios from "axios"
import type { App } from "vue"

let config: object = {}
const { VITE_PUBLIC_PATH } = import.meta.env

const setConfig = (cfg?: unknown) => {
  config = Object.assign(config, cfg) //把源对象(cfg对象)的参数的属性合并到config(目标对象)中
}

const getConfig = (key?: string): PlatformConfigs => {
  if (typeof key === "string") {
    const arr = key.split(".")
    if (arr && arr.length) {
      let data = config
      //循环arr，把arr数组每一个元素的值作为v导入方法体内的data[v]，比如data["featureFlags"],访问对象属性的方括号表示法,等于data.featureFlags，它会尝试访问 data 对象上名为 featureFlags 的属性。
      arr.forEach(v => {
        if (data && typeof data[v] !== "undefined") {
          data = data[v] // 如果路径有效，就深入一层，将 'data' 把原来指向config变量的data改成指向data.v的值
        } else {
          data = null
        }
      })
      return data
    }
  }
  return config
}

/** 获取项目动态全局配置 */
export const getPlatformConfig = async (app: App): Promise<undefined> => {
  app.config.globalProperties.$config = getConfig()
  return axios({
    method: "get",
    url: `${VITE_PUBLIC_PATH}platform-config.json`
  })
    .then(({ data: config }) => {
      let $config = app.config.globalProperties.$config
      // 自动注入系统配置
      if (app && $config && typeof config === "object") {
        $config = Object.assign($config, config)
        app.config.globalProperties.$config = $config
        // 设置全局配置
        setConfig($config)
      }
      return $config
    })
    .catch(() => {
      throw "请在public文件夹下添加platform-config.json配置文件"
    })
}

/** 本地响应式存储的命名空间 */
const responsiveStorageNameSpace = () => getConfig().ResponsiveStorageNameSpace

export { getConfig, setConfig, responsiveStorageNameSpace }
