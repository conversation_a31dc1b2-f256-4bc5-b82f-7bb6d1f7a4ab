import "@/utils/sso"
import Cookies from "js-cookie"
import { getConfig } from "@/config"
import NProgress from "@/utils/progress"
import { transformI18n } from "@/plugins/i18n"
import { buildHierarchyTree } from "@/utils/tree"
import remainingRouter from "./modules/remaining"
import { useMultiTagsStoreHook } from "@/store/modules/multiTags"
import { usePermissionStoreHook } from "@/store/modules/permission"
import { isUrl, openLink, storageLocal, isAllEmpty } from "@pureadmin/utils"
import { useUserStoreHook } from "@/store/modules/user"
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes
} from "./utils"
import {
  type Router,
  createRouter,
  type RouteRecordRaw,
  type RouteComponent
} from "vue-router"
import {
  type DataInfo,
  userKey,
  removeToken,
  multipleTabsKey
} from "@/utils/auth"

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true //访问到import.meta.glob()这个代码的时候立即加载所有模块
  }
)

/** 原始静态路由（未做任何处理） */
const routes = []

//遍历 modules 对象的所有键，并将每个键对应模块的 default 导出值添加到 routes 数组中
Object.keys(modules).forEach(key => {
  routes.push(modules[key].default)
})

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  //routes.flat(Infinity)的作用是将 routes 数组无限深度地扁平化，将其转换为一个一维数组。这通常用于处理可能包含嵌套结构的路由配置数组，使其符合路由系统的要求，确保路由配置能够被正确解析和使用。
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
)

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
).concat(...remainingRouter)

/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path
})

/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)), //转换remainingRouter为any为了兼容性绕过类型检查，将 constantRoutes 数组与展开后的 remainingRouter 数组的元素 合并 成一个新的数组。
  strict: true,
  /**
   * 路由滚动行为控制函数
   * @param to 即将进入的路由对象
   * @param from 离开的路由对象
   * @param savedPosition 只有当浏览器使用前进/后退按钮时，这个参数才可用，它包含了之前保存的滚动位置
   * @returns Promise<ScrollToOptions> 返回滚动位置配置的Promise
   */
  scrollBehavior(to, from, savedPosition) {
    // 使用Promise等待el-scrollbar渲染后再滚动才能实现滚动，没渲染前滚动是无效的
    return new Promise(resolve => {
      /**
       * 处理 Element Plus el-scrollbar 组件的滚动位置
       * 因为 Vue Router 的 scrollBehavior 默认只对浏览器原生滚动有效，
       * 对于自定义滚动容器（如 el-scrollbar）需要手动控制
       * @param scrollTop 目标滚动位置
       */
      const handleElScrollbar = (scrollTop: number) => {
        // 延迟执行，确保路由切换完成且组件已渲染
        // 100ms 的延迟可以避免在组件还未挂载时操作 DOM
        setTimeout(() => {
          // 查找 el-scrollbar 的滚动容器元素
          const scrollbarWrap = document.querySelector('.app-main .el-scrollbar__wrap')
          if (scrollbarWrap) {
            // 直接设置滚动位置
            scrollbarWrap.scrollTop = scrollTop
          }
        }, 100)
      }

      // 优先级1：检查目标路由是否强制滚动到顶部
      // 当路由配置了 scrollToTop: true 时，强制滚动到页面顶部
      if (to.meta.scrollToTop) {
        handleElScrollbar(0) // 将 el-scrollbar 滚动到顶部
        return resolve({ left: 0, top: 0 }) // 同时处理浏览器原生滚动
      }

      // 优先级2：检查是否有浏览器保存的滚动位置（前进/后退按钮）
      // 当用户使用浏览器的前进/后退按钮时，恢复之前的滚动位置
      if (savedPosition) {
        handleElScrollbar(savedPosition.top || 0) // 恢复 el-scrollbar 的滚动位置
        return resolve(savedPosition) // 恢复浏览器原生滚动位置
      }

      // 优先级3：检查来源路由是否需要保存滚动位置
      // 当从设置了 saveScrollTop: true 的路由离开时，保持当前滚动位置
      if (from.meta.saveScrollTop) {
        // 优先获取 el-scrollbar 的滚动位置，如果没有则获取浏览器原生滚动位置
        const scrollbarWrap = document.querySelector('.app-main .el-scrollbar__wrap')
        const top = scrollbarWrap ? scrollbarWrap.scrollTop :
          (document.documentElement.scrollTop || document.body.scrollTop)

        handleElScrollbar(top) // 保持 el-scrollbar 的滚动位置
        return resolve({ left: 0, top }) // 保持浏览器原生滚动位置
      }

      // 默认行为：滚动到页面顶部
      // 当没有特殊配置时，默认滚动到顶部
      handleElScrollbar(0)
      return resolve({ left: 0, top: 0 })
    })
  }
})

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name)
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      )
    }
  })
  usePermissionStoreHook().clearAllCachePage()
}

/** 路由白名单 */
const whiteList = ["/login", "/oauth/callback"]

const { VITE_HIDE_HOME } = import.meta.env

//全局前置守卫(路由导航前被调用)
router.beforeEach((to: ToRouteType, _from, next) => {
  if (to.meta?.keepAlive) {
    handleAliveRoute(to, "add") // 添加路由缓存
    // 页面整体刷新和点击标签页刷新
    if (_from.name === undefined || _from.name === "Redirect") {
      handleAliveRoute(to)
    }
  }
  const userStore = useUserStoreHook()
  const tokenFromPinia = userStore.accessToken
  const tokenFromCookie = Cookies.get("accessToken")

  NProgress.start() // 启动加载动画
  const externalLink = isUrl(to?.name as string)
  // name不是url就修改浏览器标签标题
  if (!externalLink) {
    // 遍历当前路由匹配到的所有路由记录
    to.matched.some(item => {
      if (!item.meta.title) return ""
      const Title = getConfig().Title // 获取全局配置标题
      if (Title)
        // 国际化标题拼接l加全局标题成浏览器标签标题
        document.title = `${transformI18n(item.meta.title)} | ${Title}`
      else document.title = transformI18n(item.meta.title)
    })
  }
  /** 如果已经登录并存在登录信息后不能跳转到路由白名单，而是继续保持在当前页面 */
  function toCorrectRoute() {
    // whiteList包含to的完整路径字符串，就去到来源路径，否则放行
    whiteList.includes(to.fullPath) ? next(_from.fullPath) : next()
  }
  if (tokenFromPinia || tokenFromCookie) {
    // 无权限跳转403页面,权限逻辑
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userStore.roles)) {
      next({ path: "/error/403" })
    }
    // 开启隐藏首页后在浏览器地址栏手动输入首页welcome路由则跳转到404页面
    if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
      next({ path: "/error/404" })
    }
    if (_from?.name) {
      // name为超链接
      if (externalLink) {
        openLink(to?.name as string)
        NProgress.done() // 完成加载动画
      } else {
        toCorrectRoute()
      }
    } else {
      // 刷新
      if (
        usePermissionStoreHook().wholeMenus.length === 0 &&
        to.path !== "/login"
      ) {
        initRouter().then((router: Router) => {
          if (!useMultiTagsStoreHook().getMultiTagsCache) {
            const { path } = to
            const route = findRouteByPath(
              path,
              router.options.routes[0].children
            )
            getTopMenu(true)
            // query、params模式路由传参数的标签页不在此处处理
            if (route && route.meta?.title) {
              if (isAllEmpty(route.parentId) && route.meta?.backstage) {
                // 此处为动态顶级路由（目录）
                const { path, name, meta } = route.children[0]
                useMultiTagsStoreHook().handleTags("push", {
                  path,
                  name,
                  meta
                })
              } else {
                const { path, name, meta } = route
                useMultiTagsStoreHook().handleTags("push", {
                  path,
                  name,
                  meta
                })
              }
            }
          }
          // 确保动态路由完全加入路由列表并且不影响静态路由（注意：动态路由刷新时router.beforeEach可能会触发两次，第一次触发动态路由还未完全添加，第二次动态路由才完全添加到路由列表，如果需要在router.beforeEach做一些判断可以在to.name存在的条件下去判断，这样就只会触发一次）
          if (isAllEmpty(to.name)) router.push(to.fullPath)
        })
      }
      toCorrectRoute()
    }
  } else {
    if (to.path !== "/login") {
      if (whiteList.indexOf(to.path) !== -1) {
        next()
      } else {
        removeToken()
        next({ path: "/login" })
      }
    } else {
      next()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router
