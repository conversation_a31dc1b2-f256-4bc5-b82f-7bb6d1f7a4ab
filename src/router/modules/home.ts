import { $t } from "@/plugins/i18n" // 导入国际化库
import { home } from "@/router/enums"
const { VITE_HIDE_HOME } = import.meta.env
const Layout = () => import("@/layout/index.vue")

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/welcome",
  meta: {
    icon: "ep/home-filled",
    title: $t("menus.pureHome"),
    rank: home
  },
  children: [
    {
      path: "/welcome",
      name: "Welcome",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: $t("menus.pureHome"), // 调用国际化翻译函数 $t，并传入键名 "menus.pureHome"，让函数根据当前设置的语言，返回这个键名对应的翻译文本
        showLink: VITE_HIDE_HOME === "true" ? false : true
      }
    }
  ]
} satisfies RouteConfigsTable
