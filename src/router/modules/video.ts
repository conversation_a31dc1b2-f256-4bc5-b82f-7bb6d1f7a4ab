// 最简代码，也就是这些字段必须有
import { $t } from "@/plugins/i18n"
import FluentVideoClip20Regular from "~icons/fluent/video-clip-20-regular"
export default {
  path: "/video",
  meta: {
    title: $t("menus.pureVideo"),
    icon: FluentVideoClip20Regular,
    rank: 1,
    saveScrollTop: true
  },
  children: [
    {
      path: "/video/index",
      name: "videoHome",
      component: () => import("@/views/video/index.vue"),
      meta: {
        title: $t("menus.pureVideoHome"),
        scrollToTop: true
      }
    },
    {
      path: "/video/detail",
      name: "videoDetail",
      component: () => import("@/views/video/detail.vue"),
      meta: {
        title: $t("menus.pureVideoDetail"),
        scrollToTop: true
      }
    },
    {
      path: "/video/player",
      name: "videoPlayer",
      component: () => import("@/views/video/player.vue"),
      meta: {
        title: $t("menus.pureVideoPlayer"),
        scrollToTop: true
      }
    }
  ]
} as RouteConfigsTable
