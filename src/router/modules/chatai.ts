import { chatai } from "@/router/enums"

export default {
  path: "/chatai",
  redirect: "/chatai/index",
  meta: {
    icon: "ri/chat-search-line",
    title: "chat-ai",
    rank: chatai
  },
  children: [
    {
      path: "/chatai/index",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      component: () => import("@/views/chatai/index.vue"),
      meta: {
        title: "chat-ai",
        extraIcon: "IF-pure-iconfont-new svg"
      }
    },
    {
      path: "/chatai/lobechat",
      name: "LobeChat",
      component: () => import("@/views/ai/LobeChat.vue"),
      meta: {
        title: "LobeChat",
        extraIcon: "IF-pure-iconfont-new svg"
      }
    },
    {
      path: "/chatai/nextchat",
      name: "NextChat",
      component: () => import("@/views/ai/NextChat.vue"),
      meta: {
        title: "NextChat",
        extraIcon: "IF-pure-iconfont-new svg"
      }
    }
  ]
} satisfies RouteConfigsTable
