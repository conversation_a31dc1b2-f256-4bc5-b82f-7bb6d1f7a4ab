import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios"
import type {
  PureHttpError,
  PureHttpRequestConfig,
  PureHttpResponse,
  RequestMethods
} from "./types.d"
import { stringify } from "qs"
import NProgress from "../progress"
import { formatToken, getToken } from "@/utils/auth"
import { useUserStoreHook } from "@/store/modules/user"
import { ElMessage } from 'element-plus'

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 10000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
}

class PureHttp {
  constructor() {
    //配置 Axios 的请求和响应拦截器
    this.httpInterceptorsRequest()
    this.httpInterceptorsResponse()
  }

  /** `token`过期后，暂存待执行的请求，static是类没有实例时的数据，也就是不用new直接访问"class.requests"获得的数据 */
  private static requests = []

  /** 防止重复刷新`token` */
  private static isRefreshing = false

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {}

  /** 保存当前`Axios`实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig)

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        const formattedToken = formatToken(token)
        if (formattedToken) {
          config.headers["Authorization"] = formattedToken
        }
        resolve(config)
      })
    })
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start()
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          //运行config对象里的函数
          config.beforeRequestCallback(config)
          return config
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config)
          return config
        }
        /** 请求白名单，放置一些不需要`token`的接口（通过设置请求白名单，防止`token`过期后再请求造成的死循环问题） */
        const whiteList = ["/refresh-token", "/login", "/oauth/callback"]
        // 配对数组里每个元素的url结尾是否和配置的字符串一致返回布尔值
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(resolve => {
              const token = getToken()
              if (token) {
                const now = new Date().getTime()
                const tokenData = {
                  accessToken: token,
                  accessTokenExpiresIn: now + 100000 * 60 * 60 * 2 // 2小时后过期
                }
                const expired = parseInt(tokenData.accessTokenExpiresIn) - now <= 0
                if (expired) {
                  if (!PureHttp.isRefreshing) {
                    PureHttp.isRefreshing = true
                    // token过期刷新
                    useUserStoreHook()
                      .handRefreshToken()
                      .then(res => {
                        const newAccessToken = res.data.accessToken
                        const formattedNewToken = formatToken(newAccessToken)
                        if (formattedNewToken) {
                          config.headers["Authorization"] = formattedNewToken
                        }
                        // 遍历所有因 Token 过期而等待重试的请求的回调函数 (cb)，并执行这些回调，同时将新获取到的访问令牌 (token) 传递给它们，以便它们能够使用新令牌重新发起原始请求
                        PureHttp.requests.forEach(cb => cb(newAccessToken))
                        PureHttp.requests = [] //初始化暂待请求，防止下次重复执行这个回调函数
                      })
                      .finally(() => {
                        PureHttp.isRefreshing = false
                      })
                  }
                  resolve(PureHttp.retryOriginalRequest(config))
                } else {
                  const currentAccessToken = tokenData.accessToken
                  const formattedCurrentToken = formatToken(currentAccessToken)
                  if (formattedCurrentToken) {
                    config.headers["Authorization"] = formattedCurrentToken
                  }
                  resolve(config)
                }
              } else {
                resolve(config)
              }
            })
      },
      error => {
        return Promise.reject(error)
      }
    )
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        //response为回调函数的接受到后端响应的结果，response.config为你之前请求时的原始对象config
        const $config = response.config //const $config = response.config; 和 const config = response.config; 在功能上是完全等价的。这里的 $ 只是名字的一部分。
        // 关闭进度条动画
        NProgress.done()
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response)
          return response.data
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response)
          return response.data
        }
        return response.data
      },
      (error: PureHttpError) => {
        const $error = error
        $error.isCancelRequest = Axios.isCancel($error)
        // 关闭进度条动画
        NProgress.done()
        // 所有的响应异常 区分来源为取消请求/非取消请求
        if (!$error.isCancelRequest) {
          // Display error message from backend if available
          if ($error.response && $error.response.data) {
            const backendError = $error.response.data as import("./types").BackendErrorResponse;
            if (backendError.message) {
              ElMessage.error(backendError.message);
            } else {
              ElMessage.error('Request failed, please try again later.');
            }
          } else {
            ElMessage.error('Request failed, please try again later.');
          }
        }
        return Promise.reject($error)
      }
    )
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param, //...展开操作符，把对象里所有的key:value直接赋值到目标对象
      ...axiosConfig
    } as PureHttpRequestConfig

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance //自动按照注册的顺序，运行你之前配置在 axiosInstance.interceptors.request和axiosInstance.interceptors.response上的拦截器函数。
        .request(config)
        .then((response: undefined) => {
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  }

  /** 单独抽离的`post`工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config)
  }

  /** 单独抽离的`get`工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config)
  }
}

export const http = new PureHttp()
