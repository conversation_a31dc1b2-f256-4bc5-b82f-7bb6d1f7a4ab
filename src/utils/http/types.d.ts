import type {
  Method,
  AxiosError,
  AxiosResponse,
  AxiosRequestConfig
} from "axios"

export type resultType = {
  accessToken?: string
}

// Extract<T, U>,<选择保留的筛选中类型条件（源类型T），只保留符合设置类型的目标条件（过滤条件U）>
export type RequestMethods = Extract<
  Method,
  "get" | "post" | "put" | "delete" | "patch" | "option" | "head"
>

export interface PureHttpError extends AxiosError {
  // 为了添加一个类型所以才新建扩展接口
  isCancelRequest?: boolean
}

export interface PureHttpResponse extends AxiosResponse {
  config: PureHttpRequestConfig
}

export interface PureHttpRequestConfig extends AxiosRequestConfig {
  // 声明 beforeRequestCallback方法属性的类型是一个函数
  beforeRequestCallback?: (request: PureHttpRequestConfig) => void
  beforeResponseCallback?: (response: PureHttpResponse) => void
}

export default class PureHttp {
  request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T>
  post<T, P>(
    url: string,
    params?: P,
    config?: PureHttpRequestConfig
  ): Promise<T>
  get<T, P>(url: string, params?: P, config?: PureHttpRequestConfig): Promise<T>
}

export interface BackendErrorResponse {
  message: string;
  // Add other potential backend error properties here if needed
  code?: number;
  details?: any;
}
