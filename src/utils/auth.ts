import Cookies from "js-cookie"
import { useUserStoreHook, useUserStore } from "@/store/modules/user"
import { storageLocal, isString, isIncludeAllChildren } from "@pureadmin/utils"

export interface DataInfo<T> {
  /** token */
  accessToken: string
  /** `accessToken`的过期时间 */
  accessTokenExpiresIn: T
  /** 用于调用刷新accessToken的接口时所需的token */
  refreshToken: string
  /** 头像 */
  avatar?: string
  /** 用户名 */
  username?: string
  /** 昵称 */
  nickname?: string
  /** 当前登录用户的角色 */
  roles?: Array<string>
  /** 当前登录用户的按钮级别权限 */
  permissions?: Array<string>
}

export type getTokenResult = {
  /** 用户名 */
  username: string
  /** `token` */
  accessToken: string
  /** 用于调用刷新`accessToken`的接口时所需的`token` */
  refreshToken: string
  /** `accessToken`的过期时间（ISO 8601 字符串） */
  accessTokenExpiresIn: string
}

export const userKey = "user-info"
export const TokenKey = "authorized-token"
export const accessTokenKey = "accessToken"
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs"

/** 获取`token` */
export function getToken() {
  const { accessToken } = useUserStore()
  // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
  return Cookies.get(accessTokenKey) || accessToken
    //? JSON.parse(Cookies.get(accessTokenKey)) //用key获取到的cookies的value转为js对象
     //用key获取localStorage的value值
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`accessToken`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`accessToken`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`accessToken`的过期时间（比如2小时））、`expires`（`accessToken`的过期时间）
 * 将`accessToken`、`expires`、`refreshToken`这三条信息放在key值为authorized-token的cookie里（过期自动销毁）
 * 将`avatar`、`username`、`nickname`、`roles`、`permissions`、`refreshToken`、`expires`这七条信息放在key值为`user-info`的localStorage里（利用`multipleTabsKey`当浏览器完全关闭后自动销毁）
 */
export function setToken(data: getTokenResult) {
  let expiresTimestamp = 0
  const { accessToken, refreshToken, accessTokenExpiresIn } = data // `expires` is now `expiresString`
  const userStore = useUserStoreHook()
  const { isRemembered, loginDay } = userStore // Get from store inside the function

  // expiresString is expected to be an ISO 8601 string from the API
  expiresTimestamp = new Date(accessTokenExpiresIn).getTime()
  const cookieString = JSON.stringify({
    accessToken,
    expires: expiresTimestamp,
    refreshToken
  }) // Store timestamp in cookie

  // 存储到cookie
  expiresTimestamp > 0
    ? Cookies.set(TokenKey, cookieString, {
        expires: (expiresTimestamp - Date.now()) / 86400000 // Calculate days from timestamp
      })
    : Cookies.set(TokenKey, cookieString)

  // 设置多标签页cookie
  Cookies.set(
    multipleTabsKey,
    "true",
    isRemembered
      ? {
          expires: loginDay
        }
      : {}
  )

  // 重要：将token存储到Pinia状态中
  try {
    userStore.SetAccessToken(accessToken)

    // 存储到localStorage
    const currentUserInfo =
      storageLocal().getItem<DataInfo<number>>(userKey) || {}
    storageLocal().setItem(userKey, {
      ...currentUserInfo,
      accessToken,
      refreshToken,
      expires: expiresTimestamp
    })

    console.log("Token已成功存储到Pinia和localStorage", {
      accessToken: accessToken.substring(0, 10) + "...",
      refreshToken: refreshToken.substring(0, 10) + "..."
    })
  } catch (error) {
    console.error("存储token到Pinia失败:", error)
  }
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  Cookies.remove(TokenKey)
  Cookies.remove(multipleTabsKey)
  Cookies.remove("accessToken")
  Cookies.remove("refreshToken")
  storageLocal().removeItem(userKey)

  // 在运行时安全地获取store并清除accessToken
  try {
    const userStore = useUserStoreHook()
    userStore.SetAccessToken("")
  } catch (error) {
    console.warn("无法访问userStore，可能Pinia尚未初始化")
  }
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string | undefined => {
  if (typeof token !== "string" || !token) {
    return undefined // 或者返回空字符串 ""，取决于后续逻辑如何处理
  }
  if (token.startsWith("Bearer ")) {
    return token
  }
  return "Bearer " + token
}

/** 是否有按钮级别的权限（根据登录接口返回的`permissions`字段进行判断）*/
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false
  const allPerms = "*:*:*"

  try {
    const { permissions } = useUserStoreHook()
    if (!permissions) return false
    if (permissions.length === 1 && permissions[0] === allPerms) return true
    const isAuths = isString(value)
      ? permissions.includes(value)
      : isIncludeAllChildren(value, permissions)
    return isAuths ? true : false
  } catch (error) {
    console.warn("无法访问permissions，可能Pinia尚未初始化")
    return false
  }
}
