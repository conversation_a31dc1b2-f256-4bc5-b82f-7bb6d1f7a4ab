export function formatISODate(isoString: string) {
  if (!isoString) return "" // 处理空值
  try {
    const date = new Date(isoString)
    if (isNaN(date.getTime())) {
      // 检查日期是否有效
      console.error("Invalid date string:", isoString)
      return isoString // 返回原始字符串或错误提示
    }
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, "0")
    const day = date.getDate().toString().padStart(2, "0")
    const hours = date.getHours().toString().padStart(2, "0")
    const minutes = date.getMinutes().toString().padStart(2, "0")
    return `${year}年${month}月${day}日 ${hours}时${minutes}分`
  } catch (error) {
    console.error("Error formatting date:", isoString, error)
    return isoString // 出错时返回原始字符串
  }
}
