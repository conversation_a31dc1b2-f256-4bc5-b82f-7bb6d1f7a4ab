<script setup lang="ts">
import dayjs from "dayjs"
import { getMineLogs } from "@/api/user"
import { reactive, ref, onMounted } from "vue"
import { deviceDetection } from "@pureadmin/utils"
import type { PaginationProps } from "@pureadmin/table"

defineOptions({
  name: "SecurityLog"
})

const loading = ref(true)
const dataList = ref([])
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  layout: "prev, pager, next"
})

// 使用pagination中的值作为查询参数
const getPageParams = () => {
  return {
    pageNum: pagination.currentPage,
    pageSize: pagination.pageSize
  }
}

const columns: TableColumnList = [
  {
    label: "详情",
    prop: "summary",
    minWidth: 140
  },
  {
    label: "IP 地址",
    prop: "ip",
    minWidth: 100
  },
  {
    label: "地点",
    prop: "address",
    minWidth: 140
  },
  {
    label: "操作系统",
    prop: "systemInfo",
    minWidth: 100
  },
  {
    label: "浏览器类型",
    prop: "browser",
    minWidth: 100
  },
  {
    label: "时间",
    prop: "operatingTime",
    minWidth: 180,
    formatter: ({ operatingTime }) =>
      dayjs(operatingTime).format("YYYY-MM-DD HH:mm:ss")
  }
]

async function onSearch() {
  loading.value = true
  try {
    const result = await getMineLogs(getPageParams())
    if (result.success && result.data) {
      // 根据后端返回的数据结构调整
      dataList.value = result.data.list // 数据直接在data数组中
      if (result.data.pageResult) {
        pagination.total = result.data.pageResult.total || 0
        pagination.pageSize = result.data.pageResult.pageSize || 10
        pagination.currentPage = result.data.pageResult.pageNum || 1
      }
      console.log("加载页面数据成功:", dataList.value)
    } else {
      dataList.value = []
      pagination.total = 0
      console.log("无数据或请求失败")
    }
  } catch (error) {
    console.error("加载数据出错:", error)
    dataList.value = []
    pagination.total = 0
  } finally {
    setTimeout(() => {
      loading.value = false
    }, 200)
  }
}

// 处理分页变化事件
function handlePageChange(page: number) {
  console.log("分页切换到:", page)
  pagination.currentPage = page
  onSearch()
}

onMounted(() => {
  onSearch()
})
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">安全日志</h3>
    <pure-table
      row-key="id"
      table-layout="auto"
      :loading="loading"
      :data="dataList"
      :columns="columns"
      :pagination="pagination"
      @page-current-change="handlePageChange"
    />
  </div>
</template>
