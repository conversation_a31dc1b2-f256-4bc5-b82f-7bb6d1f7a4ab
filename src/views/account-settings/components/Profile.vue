<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import { message } from "@/utils/message"
import { getMine, type UserInfo } from "@/api/user"
import type { FormInstance, FormRules } from "element-plus"
import ReCropperPreview from "@/components/ReCropperPreview"
import { createFormData, deviceDetection } from "@pureadmin/utils"
import uploadLine from "~icons/ri/upload-line"
import { useUserStoreHook } from "@/store/modules/user"
import { updateUserInfo } from "@/api/user"
import { http } from "@/utils/http"

defineOptions({
  name: "Profile"
})

const imgSrc = ref("")
const cropperBlob = ref()
const cropRef = ref()
const uploadRef = ref()
const isShow = ref(false)
const userInfoFormRef = ref<FormInstance>()
const loading = ref(false)
const userStore = useUserStoreHook()
const currentUsername = userStore.username

const userInfos = reactive({
  avatar: "",
  nickname: "",
  email: "",
  phone: "",
  description: ""
})

const rules = reactive<FormRules<UserInfo>>({
  nickname: [{ required: true, message: "昵称必填", trigger: "blur" }]
})

function queryEmail(queryString, callback) {
  const emailList = [
    { value: "@qq.com" },
    { value: "@126.com" },
    { value: "@163.com" }
  ]
  let results = []
  let queryList = []
  emailList.map(item =>
    queryList.push({ value: queryString.split("@")[0] + item.value })
  )
  results = queryString
    ? queryList.filter(
        item =>
          item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      )
    : queryList
  callback(results)
}

// 选择文件时触发，但不自动上传
const onChange = uploadFile => {
  const reader = new FileReader()
  reader.onload = e => {
    // 把结果值传递给编辑头像工具
    imgSrc.value = e.target.result as string
    // 显示编辑头像工具
    isShow.value = true
  }
  // 读取选定内容并转换为Base64编码的Data字符串
  reader.readAsDataURL(uploadFile.raw)
}

const handleClose = () => {
  cropRef.value.hidePopover()
  uploadRef.value.clearFiles()
  isShow.value = false
}

// 编辑图像完后触发
// onCropper函数接收包含裁剪后图片数据（Blob格式）的对象，并将Blob存入cropperBlob引用
const onCropper = ({ blob }) => (cropperBlob.value = blob)

interface UploadResponse {
  success: boolean
  data:
    | string
    | {
        url: string
        avatarUrl: string
      }
  message?: string
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    const res = await getMine()
    Object.assign(userInfos, res.data)
    userStore.SetPhone(res.data.phone)
    userStore.SetEmail(res.data.email)
  } catch (error) {
    console.error("获取用户信息失败:", error)
    message("获取用户信息失败，请稍后重试", { type: "error" })
  } finally {
    loading.value = false
  }
}

// 手动处理文件上传，编辑头像工具点击确定后执行的方法
const handleSubmitImage = async () => {
  // 防止未选择文件或未完成裁剪时点击上传
  if (!cropperBlob.value) {
    message("请先选择并裁剪图片", { type: "warning" })
    return
  }

  let newAvatarUrl = "" // 用于临时存储新头像URL

  try {
    loading.value = true

    // --- 步骤 1: 上传文件 ---
    const blob = cropperBlob.value
    // 将cropperBlob转为File对象，默认命名为"avatar.png"
    const imageFile = new File([blob], "avatar.png", { type: "image/png" })
    const formData = new FormData()
    // 创建FormData对象（115行），添加imageFile到"file"键并设置类型为"png"。FormData用于HTTP请求发送文件
    formData.append("file", imageFile)
    formData.append("type", "png")

    // 上传头像文件
    const uploadResponse = await http.request<UploadResponse>(
      "post",
      "/api/file/upload-general",
      {
        data: formData,
        headers: { "Content-Type": undefined },
        transformRequest: [data => data]
      }
    )
    if (!uploadResponse.success) {
      message("头像上传失败: " + (uploadResponse.message || "未知错误"), {
        type: "error"
      })
      loading.value = false
      return // 上传失败，提前退出
    }

    // 获取新头像URL
    newAvatarUrl =
      typeof uploadResponse.data === "string"
        ? uploadResponse.data
        : uploadResponse.data.avatarUrl

    if (!newAvatarUrl) {
      message("头像上传成功，但未获取到有效URL", { type: "warning" })
      loading.value = false
      return // 未获取到有效URL，也退出
    }

    // --- 步骤 2: 更新成功后，再更新本地状态 ---
    userInfos.avatar = newAvatarUrl
    // 同步更新 Pinia store 中的头像
    userStore.SET_AVATAR(newAvatarUrl)
    message("更新头像成功", { type: "success" })
    handleClose() // 关闭弹窗
  } catch (error) {
    console.error("更新头像过程中发生错误:", error)
    // 根据错误来源给出不同提示
    if (newAvatarUrl) {
      // 如果URL已获取，说明是更新用户信息步骤失败
      message(
        `头像已上传，但更新用户信息失败: ${error.message || "未知错误"}`,
        { type: "error" }
      )
    } else {
      // 否则是文件上传步骤失败
      message(`头像上传失败: ${error.message || "未知错误"}`, {
        type: "error"
      })
    }
  } finally {
    loading.value = false
  }
}

// 更新信息
const onSubmit = async (formEl: FormInstance) => {
  if (!formEl) return
  loading.value = true

  try {
    const valid = await formEl.validate()
    if (valid) {
      await updateUserInfo(userInfos)
      message("更新信息成功", { type: "success" })
    }
  } catch (error) {
    console.error("更新信息失败:", error)
    message("更新信息失败，请稍后重试", { type: "error" })
  } finally {
    loading.value = false
  }
}

// 使用onMounted生命周期钩子，确保组件挂载后再获取数据
onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">个人信息</h3>
    <el-form
      ref="userInfoFormRef"
      v-loading="loading"
      label-position="top"
      :rules="rules"
      :model="userInfos"
    >
      <el-form-item label="头像">
        <el-avatar :size="80" :src="userInfos.avatar" />
        <!-- 
          el-upload组件说明:
          1. action为空字符串，表示禁用自动上传功能,因为要选择图片大小所以禁用自动上传
          2. :auto-upload="false" 禁用自动上传，选择文件后不会立即发送请求
          3. :on-change 当选择文件后触发此函数，但不会自动上传
          4. 实际上传在裁剪后通过我们自己的handleSubmitImage函数手动执行
        -->
        <el-upload
          ref="uploadRef"
          accept="image/*"
          action=""
          :limit="1"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="onChange"
          name="file"
        >
          <el-button plain class="ml-4">
            <IconifyIconOffline :icon="uploadLine" />
            <span class="ml-2">更新头像</span>
          </el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="userInfos.nickname" placeholder="请输入昵称" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-autocomplete
          v-model="userInfos.email"
          :fetch-suggestions="queryEmail"
          :trigger-on-focus="false"
          placeholder="请输入邮箱"
          clearable
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input
          v-model="userInfos.phone"
          placeholder="请输入联系电话"
          clearable
        />
      </el-form-item>
      <el-form-item label="简介">
        <el-input
          v-model="userInfos.description"
          placeholder="请输入简介"
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 8 }"
          maxlength="56"
          show-word-limit
        />
      </el-form-item>
      <el-button type="primary" @click="onSubmit(userInfoFormRef)">
        更新信息
      </el-button>
    </el-form>
    <el-dialog
      v-model="isShow"
      width="40%"
      title="编辑头像"
      destroy-on-close
      :closeOnClickModal="false"
      :before-close="handleClose"
      :fullscreen="deviceDetection()"
    >
      <ReCropperPreview ref="cropRef" :imgSrc="imgSrc" @cropper="onCropper" />
      <template #footer>
        <div class="dialog-footer">
          <el-button bg text @click="handleClose">取消</el-button>
          <el-button bg text type="primary" @click="handleSubmitImage">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
