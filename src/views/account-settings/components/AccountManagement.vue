<script setup lang="ts">
import { ref } from "vue"
import { message } from "@/utils/message"
import { deviceDetection } from "@pureadmin/utils"
import { useUserStoreHook } from "@/store/modules/user"
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange"
import { ElMessageBox, ElMessage } from "element-plus"

defineOptions({
  name: "AccountManagement"
})

const { onReset } = useDataThemeChange()
const userStore = useUserStoreHook()
const phone = userStore.phone
const email = userStore.email

const list = ref([
  {
    title: "账户密码",
    illustrate: "当前密码强度：中",
    button: "修改"
  },
  {
    title: "密保手机",
    illustrate: "已绑定手机: " + (phone ? phone : "未绑定")
  },
  {
    title: "备用邮箱",
    illustrate: "已绑定邮箱: " + (email ? email : "未绑定")
  }
])

function onClick(item) {
  if (item.title === "账户密码") {
    ElMessageBox.confirm(
      '你想要退出并跳转到修改密码页面吗',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        ElMessage({
          type: 'success',
          message: 'completed',
        })
        onReset(4)
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: 'canceled',
        })
      })
  }else {
    message("请根据具体业务自行实现", { type: "success" })
}}
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">账户管理</h3>
    <div v-for="(item, index) in list" :key="index">
      <div class="flex items-center">
        <div class="flex-1">
          <p>{{ item.title }}</p>
          <el-text class="mx-1" type="info">{{ item.illustrate }}</el-text>
        </div>
        <el-button type="primary" text @click="onClick(item)">
          {{ item.button }}
        </el-button>
      </div>
      <el-divider />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.el-divider--horizontal {
  border-top: 0.1px var(--el-border-color) var(--el-border-style);
}
</style>
