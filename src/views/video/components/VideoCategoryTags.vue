<script setup lang="ts">
import { defineProps, defineEmits, PropType } from "vue"

interface Category {
  id: number | string
  name: string
}

const props = defineProps({
  categories: {
    type: Array as PropType<Category[]>,
    required: true
  },
  activeCategory: {
    type: String,
    required: true
  },
  interactive: {
    type: Boolean,
    default: true
  },
  showAllTag: {
    type: Boolean,
    default: false
  },
  allTagText: {
    type: String,
    default: "全部"
  },
  allTagValue: {
    type: String,
    default: ""
  }
})

const emit = defineEmits(["category-selected"])

const handleTagClick = (categoryName: string) => {
  if (props.interactive) {
    emit("category-selected", categoryName)
  }
}
</script>

<template>
  <div class="category-tags-wrapper">
    <el-tag
      v-if="showAllTag"
      class="category-tag"
      :class="{ active: interactive && activeCategory === allTagValue }"
      @click="handleTagClick(allTagValue)"
      :effect="interactive && activeCategory === allTagValue ? 'dark' : 'light'"
      :type="interactive && activeCategory === allTagValue ? undefined : 'info'"
    >
      {{ allTagText }}
    </el-tag>
    <el-tag
      v-for="category in categories"
      :key="category.id"
      class="category-tag"
      :class="{ active: interactive && activeCategory === category.name }"
      @click="handleTagClick(category.name)"
      :effect="
        interactive && activeCategory === category.name ? 'dark' : 'light'
      "
      :type="
        interactive && activeCategory === category.name ? undefined : 'info'
      "
    >
      {{ category.name }}
    </el-tag>
  </div>
</template>

<style scoped>
.category-tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  padding: 5px 0;
}

.category-tag {
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  background-color: #ffffff;
  border: 2px solid #e2e8f0;
  height: 42px;
  min-width: 80px;
  text-align: center;
  color: #4a5568;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.category-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: left 0.3s ease;
  z-index: 0;
}

.category-tag:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.category-tag:hover::before {
  left: -70%;
}

.category-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  transform: scale(1.05);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
}

.category-tag.active::before {
  left: 0;
}

.category-tag span {
  position: relative;
  z-index: 1;
}

/* 点击效果 */
.category-tag:active {
  transform: scale(0.95);
}

/* 响应式交互 */
.category-tag {
  cursor: v-bind("interactive ? 'pointer' : 'default'");
  user-select: v-bind("interactive ? 'none' : 'auto'");
}

/* 非交互状态 */
.category-tag:not(.active):not(:hover) {
  background: #f8f9fa;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.category-tag.active {
  animation: pulse 2s infinite;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .category-tags-wrapper {
    gap: 10px;
  }
  
  .category-tag {
    padding: 8px 16px;
    font-size: 14px;
    height: 38px;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .category-tag {
    padding: 6px 12px;
    font-size: 13px;
    height: 34px;
    min-width: 60px;
  }
}
</style>
