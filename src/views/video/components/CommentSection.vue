<script setup lang="ts">
import { ref, computed, nextTick, watch } from "vue"
import { useUserStore } from "@/store/modules/user"
import { ElMessage, ElMessageBox } from "element-plus"
import {
  sendCommentApi,
  getCommentList<PERSON>pi,
  deleteComment<PERSON><PERSON>,
  createVideoEpisodeCommentLikesApi,
  getVideoEpisodeCommentLikesApi
} from "@/api/videoCommentInteraction"
import IconamoonLikeLight from "~icons/iconamoon/like-light"
import IconamoonLikeFill from "~icons/iconamoon/like-fill"
import IconamoonDislikeLight from "~icons/iconamoon/dislike-light"
import IconamoonDislikeFill from "~icons/iconamoon/dislike-fill"

defineOptions({
  name: "CommentSection"
})

const props = defineProps({
  episodeId: {
    type: Number,
    required: true
  }
})

const commentList = ref([])
const comment = ref("")
const currentUserAvatar = ref(useUserStore().avatar)
const currentUsername = ref(useUserStore().username)
const replyText = ref("")
const showReplyInputForCommentId = ref<number | null>(null)
const replyingToUsername = ref<string | null>(null)

// 过滤出主评论（parentCommentId 为 null 或 undefined），并按时间降序排列
const commentListComputed = computed(() => {
  return commentList.value
    .filter(c => c.parentCommentId === null || c.parentCommentId === undefined)
    .sort(
      (a, b) =>
        new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime()
    )
})

// 获取特定评论的所有回复
const getRepliesForComment = (parentCommentId: number) => {
  // 从整个评论列表中筛选出 parentCommentId 匹配的回复，并按时间升序排列
  return commentList.value
    .filter(c => c.parentCommentId === parentCommentId)
    .sort(
      (a, b) =>
        new Date(a.createdTime).getTime() - new Date(b.createdTime).getTime()
    )
}

// 发送评论或回复
const handleSendComment = async (
  commentContent: string,
  parentId: number | null
) => {
  if (!commentContent.trim()) {
    ElMessage.warning("评论内容不能为空。")
    return
  }
  try {
    const res = await sendCommentApi({
      content: commentContent,
      videoEpisodesId: props.episodeId,
      parentCommentId: parentId
    })
    if (res.success) {
      if (parentId) {
        // 如果是回复，清空回复输入框并隐藏
        replyText.value = ""
        showReplyInputForCommentId.value = null
        replyingToUsername.value = null
      } else {
        // 如果是主评论，清空主评论输入框
        comment.value = ""
      }
      // 重新获取评论列表以显示最新评论
      await getCommentList()
    } else {
      ElMessage.error(`发送评论失败: ${res.message}`)
    }
  } catch (error) {
    ElMessage.error(`发送评论时出错: ${error}`)
  }
}

// 切换回复输入框的显示状态
const toggleReplyInput = (commentId: number, username?: string) => {
  // 如果当前点击的回复框已经打开，则关闭它
  if (showReplyInputForCommentId.value === commentId) {
    showReplyInputForCommentId.value = null
    replyingToUsername.value = null
    replyText.value = "" // 关闭时清空内容
  } else {
    // 否则，打开对应 commentId 的回复框
    showReplyInputForCommentId.value = commentId
    replyingToUsername.value = username || null // 存储被回复者的用户名
    replyText.value = "" // 重置回复文本
    // 使用 nextTick 确保 DOM 更新后，聚焦到输入框
    nextTick(() => {
      const activeReplyWrapper = document.querySelector(
        `.reply-input-wrapper.active-reply-${commentId}`
      )
      if (activeReplyWrapper) {
        const inputElement = activeReplyWrapper.querySelector(
          "textarea"
        ) as HTMLElement | null
        inputElement?.focus()
      } else {
        // 备用选择器，以防万一
        const inputElement = document.querySelector(
          `.reply-input-field-${commentId} textarea`
        ) as HTMLElement | null
        inputElement?.focus()
      }
    })
  }
}

// 获取评论列表
const getCommentList = async () => {
  if (!props.episodeId) return
  try {
    const res = await getCommentListApi({
      videoEpisodesId: props.episodeId,
      pageNum: 1,
      pageSize: 100 // 获取较多评论以包含回复，未来可考虑分页
    })
    if (res.success && res.data) {
      commentList.value = res.data.list
    } else {
      commentList.value = []
    }
  } catch (error) {
    commentList.value = []
  }
}

const deleteComment = (id: number) => {
  ElMessageBox.confirm("你确定要删除这条评论吗", "小心", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      await deleteCommentApi(id).then(async res => {
        if (res.success == true) {
          await getCommentList()
        }
      })
      ElMessage({
        type: "success",
        message: "删除成功"
      })
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除"
      })
    })
}

// 监听 episodeId 的变化，并在变化时重新获取评论列表
watch(
  () => props.episodeId,
  newEpisodeId => {
    if (newEpisodeId) {
      getCommentList()
    }
  },
  { immediate: true }
)

// 用户点赞评论
const commentLike = async (commentId: number, type: "like" | "dislike") => {
  let res = await createVideoEpisodeCommentLikesApi({
    commentId,
    type
  })
  if (res.success) {
    getCommentLike(commentId)
  }
}

// 点赞评论后更新那个点赞的评论的数量
const getCommentLike = async (commentId: number) => {
  let res = await getVideoEpisodeCommentLikesApi(commentId)
  if (res.success) {
    let foundComment = commentList.value.find(item => item.id == commentId)
    if (foundComment) {
        foundComment.likesCount = res.data.likeCount
        foundComment.dislikesCount = res.data.dislikeCount
        foundComment.userLikeType=res.data.userLikeType
        foundComment.likeStatus = res.data.likeStatus
      } 
  }
}
</script>

<template>
  <div>
    <!--评论输入框和头像-->
    <div class="comment-container main-comment-input-container">
      <div class="comment-area-avatar">
        <el-avatar :size="50" :src="currentUserAvatar" />
      </div>
      <div class="comment-area-input">
        <el-input
          type="textarea"
          :rows="4"
          v-model="comment"
          clearable
          placeholder="请输入你的评论"
        />
      </div>
      <div class="comment-area-button">
        <el-button type="primary" @click="handleSendComment(comment, null)"
          >发送</el-button
        >
      </div>
    </div>

    <!--用户评论-->
    <div v-if="commentListComputed.length > 0" class="comments-section">
      <div
        v-for="commentItem in commentListComputed"
        :key="commentItem.id"
        class="comment-thread"
      >
        <!-- 单个评论 -->
        <div class="comment-block main-comment-block">
          <div class="comment-container">
            <div class="comment-area-avatar">
              <el-avatar :size="50" :src="commentItem.avatar" />
            </div>
            <div class="comment-details">
              <div class="comment-area-username">
                {{ commentItem.username }}
              </div>
              <div class="comment-area-content">
                {{ commentItem.content }}
              </div>
              <div class="timeAndReply-container">
                <div class="comment-area-time">
                  {{ commentItem.createdTime }}
                </div>
                <div class="comment-area-interaction">
                  <div
                    class="comment-like"
                    @click="commentLike(commentItem.id, 'like')"
                  >
                    <IconamoonLikeFill
                      v-if="
                        commentItem.likeStatus == true &&
                        commentItem.userLikeType == 'like'
                      "
                      class="comment-likeAndDislike-icon"
                    />
                    <IconamoonLikeLight
                      v-else
                      class="comment-likeAndDislike-icon"
                    />
                    {{ commentItem.likesCount }}
                  </div>
                  <div
                    class="comment-dislike"
                    @click="commentLike(commentItem.id, 'dislike')"
                  >
                    <IconamoonDislikeFill
                      v-if="
                        commentItem.likeStatus == true &&
                        commentItem.userLikeType == 'dislike'
                      "
                      class="comment-likeAndDislike-icon"
                    />
                    <IconamoonDislikeLight
                      v-else
                      class="comment-likeAndDislike-icon"
                    />
                    {{ commentItem.dislikesCount }}
                  </div>
                  
                </div>
                <div
                  class="comment-area-reply"
                  @click="
                    toggleReplyInput(commentItem.id, commentItem.username)
                  "
                >
                  回复
                </div>
                <div
                  v-if="commentItem.username === currentUsername"
                  class="comment-area-delete"
                  @click="deleteComment(commentItem.id)"
                >
                  删除
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 回复输入区域 -->
        <div
          v-if="showReplyInputForCommentId === commentItem.id"
          :class="['reply-input-wrapper', `active-reply-${commentItem.id}`]"
        >
          <div class="comment-container reply-input-container">
            <div class="comment-area-avatar">
              <el-avatar :size="40" :src="currentUserAvatar" />
            </div>
            <div class="comment-area-input reply-input-field">
              <el-input
                type="textarea"
                :rows="2"
                v-model="replyText"
                clearable
                :placeholder="
                  replyingToUsername
                    ? `回复 @${replyingToUsername}`
                    : '请输入回复...'
                "
                :class="`reply-input-field-${commentItem.id}`"
              />
            </div>
            <div class="reply-buttons">
              <el-button
                size="small"
                type="primary"
                @click="handleSendComment(replyText, commentItem.id)"
                >回复</el-button
              >
              <el-button size="small" @click="toggleReplyInput(commentItem.id)"
                >取消</el-button
              >
            </div>
          </div>
        </div>

        <!-- 评论的回复内容 -->
        <div
          v-if="getRepliesForComment(commentItem.id).length > 0"
          class="replies-container first-level-replies"
        >
          <div
            v-for="reply1 in getRepliesForComment(commentItem.id)"
            :key="reply1.id"
            class="comment-thread-reply"
          >
            <div class="comment-block reply-block">
              <div class="comment-container">
                <div class="comment-area-avatar">
                  <el-avatar :size="40" :src="reply1.avatar" />
                </div>
                <div class="comment-details">
                  <div class="comment-area-username">
                    {{ reply1.username }}
                    <span v-if="commentItem.username" class="reply-to-text"
                      >回复 @{{ commentItem.username }}</span
                    >
                  </div>
                  <div class="comment-area-content">
                    {{ reply1.content }}
                  </div>
                  <div class="timeAndReply-container">
                    <div class="comment-area-time">
                      {{ reply1.createdTime }}
                    </div>
                    <div class="comment-area-interaction">
                      <div
                        class="comment-like"
                        @click="commentLike(reply1.id, 'like')"
                      >
                        <IconamoonLikeFill
                          v-if="
                            reply1.likeStatus == true &&
                            reply1.userLikeType == 'like'
                          "
                          class="comment-likeAndDislike-icon"
                        />
                        <IconamoonLikeLight
                          v-else
                          class="comment-likeAndDislike-icon"
                        />
                        {{ reply1.likesCount }}
                      </div>
                      <div
                        class="comment-dislike"
                        @click="commentLike(reply1.id, 'dislike')"
                      >
                        <IconamoonDislikeFill
                          v-if="
                            reply1.likeStatus == true &&
                            reply1.userLikeType == 'dislike'
                          "
                          class="comment-likeAndDislike-icon"
                        />
                        <IconamoonDislikeLight
                          v-else
                          class="comment-likeAndDislike-icon"
                        />
                        {{ reply1.dislikesCount }}
                      </div>
                      
                    </div>
                    <div
                      class="comment-area-reply"
                      @click="toggleReplyInput(reply1.id, reply1.username)"
                    >
                      回复
                    </div>
                    <div
                      v-if="reply1.username === currentUsername"
                      class="comment-area-delete"
                      @click="deleteComment(reply1.id)"
                    >
                      删除
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 回复输入区域：回复一级回复 (reply1) -->
            <div
              v-if="showReplyInputForCommentId === reply1.id"
              :class="[
                'reply-input-wrapper',
                'reply-to-reply-input-wrapper',
                `active-reply-${reply1.id}`
              ]"
            >
              <div class="comment-container reply-input-container">
                <div class="comment-area-avatar">
                  <el-avatar :size="30" :src="currentUserAvatar" />
                </div>
                <div class="comment-area-input reply-input-field">
                  <el-input
                    type="textarea"
                    :rows="2"
                    v-model="replyText"
                    clearable
                    :placeholder="
                      replyingToUsername
                        ? `回复 @${replyingToUsername}`
                        : '请输入回复...'
                    "
                    :class="`reply-input-field-${reply1.id}`"
                  />
                </div>
                <div class="reply-buttons">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleSendComment(replyText, reply1.id)"
                    >回复</el-button
                  >
                  <el-button size="small" @click="toggleReplyInput(reply1.id)"
                    >取消</el-button
                  >
                </div>
              </div>
            </div>

            <!-- 二级回复列表 (level 2 replies to reply1) -->
            <div
              v-if="getRepliesForComment(reply1.id).length > 0"
              class="replies-container second-level-replies"
            >
              <div
                v-for="reply2 in getRepliesForComment(reply1.id)"
                :key="reply2.id"
                class="comment-thread-reply"
              >
                <div class="comment-block reply-block reply-to-reply-block">
                  <div class="comment-container">
                    <div class="comment-area-avatar">
                      <el-avatar :size="30" :src="reply2.avatar" />
                    </div>
                    <div class="comment-details">
                      <div class="comment-area-username">
                        {{ reply2.username }}
                        <span v-if="reply1.username" class="reply-to-text"
                          >回复 @{{ reply1.username }}</span
                        >
                      </div>
                      <div class="comment-area-content">
                        {{ reply2.content }}
                      </div>
                      <div class="timeAndReply-container">
                        <div class="comment-area-time">
                          {{ reply2.createdTime }}
                        </div>
                        <div class="comment-area-interaction">
                          <div
                            class="comment-like"
                            @click="commentLike(reply2.id, 'like')"
                          >
                            <IconamoonLikeFill
                              v-if="
                                reply2.likeStatus == true &&
                                reply2.userLikeType == 'like'
                              "
                              class="comment-likeAndDislike-icon"
                            />
                            <IconamoonLikeLight
                              v-else
                              class="comment-likeAndDislike-icon"
                            />
                            {{ reply2.likesCount }}
                          </div>
                          <div
                            class="comment-dislike"
                            @click="commentLike(reply2.id, 'dislike')"
                          >
                            <IconamoonDislikeFill
                              v-if="
                                reply2.likeStatus == true &&
                                reply2.userLikeType == 'dislike'
                              "
                              class="comment-likeAndDislike-icon"
                            />
                            <IconamoonDislikeLight
                              v-else
                              class="comment-likeAndDislike-icon"
                            />
                            {{ reply2.dislikesCount }}
                          </div>
                        </div>
                        <div
                          class="comment-area-reply"
                          @click="toggleReplyInput(reply2.id, reply2.username)"
                        >
                          回复
                        </div>
                        <div
                          v-if="reply2.username === currentUsername"
                          class="comment-area-delete"
                          @click="deleteComment(reply2.id)"
                        >
                          删除
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 回复输入区域：回复二级回复 (reply2) -->
                <div
                  v-if="showReplyInputForCommentId === reply2.id"
                  :class="[
                    'reply-input-wrapper',
                    'reply-to-reply-input-wrapper',
                    'reply-to-reply-input-wrapper-level-3',
                    `active-reply-${reply2.id}`
                  ]"
                >
                  <div class="comment-container reply-input-container">
                    <div class="comment-area-avatar">
                      <el-avatar :size="25" :src="currentUserAvatar" />
                    </div>
                    <div class="comment-area-input reply-input-field">
                      <el-input
                        type="textarea"
                        :rows="2"
                        v-model="replyText"
                        clearable
                        :placeholder="
                          replyingToUsername
                            ? `回复 @${replyingToUsername}`
                            : '请输入回复...'
                        "
                        :class="`reply-input-field-${reply2.id}`"
                      />
                    </div>
                    <div class="reply-buttons">
                      <el-button
                        size="small"
                        type="primary"
                        @click="handleSendComment(replyText, reply2.id)"
                        >回复</el-button
                      >
                      <el-button
                        size="small"
                        @click="toggleReplyInput(reply2.id)"
                        >取消</el-button
                      >
                    </div>
                  </div>
                </div>

                <!-- 三级回复列表 (level 3 replies to reply2) -->
                <div
                  v-if="getRepliesForComment(reply2.id).length > 0"
                  class="replies-container third-level-replies"
                >
                  <div
                    v-for="reply3 in getRepliesForComment(reply2.id)"
                    :key="reply3.id"
                    class="comment-block reply-block reply-to-reply-block reply-to-reply-block-level-3"
                  >
                    <div class="comment-container">
                      <div class="comment-area-avatar">
                        <el-avatar :size="25" :src="reply3.avatar" />
                      </div>
                      <div class="comment-details">
                        <div class="comment-area-username">
                          {{ reply3.username }}
                          <span v-if="reply2.username" class="reply-to-text"
                            >回复 @{{ reply2.username }}</span
                          >
                        </div>
                        <div class="comment-area-content">
                          {{ reply3.content }}
                        </div>
                        <div class="timeAndReply-container">
                          <div class="comment-area-time">
                            {{ reply3.createdTime }}
                          </div>
                          <div class="comment-area-interaction">
                            <div
                              class="comment-like"
                              @click="commentLike(reply3.id, 'like')"
                            >
                              <IconamoonLikeFill
                                v-if="
                                  reply3.likeStatus == true &&
                                  reply3.userLikeType == 'like'
                                "
                                class="comment-likeAndDislike-icon"
                              />
                              <IconamoonLikeLight
                                v-else
                                class="comment-likeAndDislike-icon"
                              />
                              {{ reply3.likesCount }}
                            </div>
                            <div
                              class="comment-dislike"
                              @click="commentLike(reply3.id, 'dislike')"
                            >
                              <IconamoonDislikeFill
                                v-if="
                                  reply3.likeStatus == true &&
                                  reply3.userLikeType == 'dislike'
                                "
                                class="comment-likeAndDislike-icon"
                              />
                              <IconamoonDislikeLight
                                v-else
                                class="comment-likeAndDislike-icon"
                              />
                              {{ reply3.dislikesCount }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="no-comments">暂无评论，快来抢沙发吧！</div>
  </div>
</template>

<style scoped>
/* 评论区样式美化 */
.comment-area-input {
  margin-left: 1.5rem;
  width: 39rem;
}

.comment-area-input :deep(.el-textarea__inner) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 15px;
  transition: all 0.3s ease;
}

.comment-area-input :deep(.el-textarea__inner:focus) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.comment-area-input :deep(.el-textarea__inner::placeholder) {
  color: #405783;
}

.comment-container {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px ;
  transition: all 0.3s ease;
}

.comment-container:hover {
  background: rgba(255, 255, 255, 0.08);
}

.comment-container.main-comment-input-container {
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 10rem;
}

.comment-details {
  display: flex;
  flex-direction: column;
  margin-left: 1.2rem;
  flex-grow: 1;
}

.comment-area-avatar :deep(.el-avatar) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.comment-area-username {
  font-weight: 600;
  color: #667eea;
  font-size: 16px;
  margin-bottom: 8px;
}

.timeAndReply-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 15px;
}

.comment-area-content {
  margin-top: 0.5rem;
  word-break: break-word;
  color: #405783;
  font-size: 15px;
  line-height: 1.6;
}

.comment-area-time {
  font-size: 13px;
  color: #405783;
}

.comment-area-interaction {
  display: flex;
  align-items: center;
  gap: 10px;
}
.comment-like{
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}
.comment-dislike{
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.comment-likeAndDislike-icon {
  width: 16px;
  height: 16px;
}

.comment-area-reply,
.comment-area-delete {
  font-size: 13px;
  color: #667eea;
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 6px;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.comment-area-reply:hover,
.comment-area-delete:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #8b9ff5;
  text-decoration: none;
  transform: translateY(-1px);
}

.comment-area-button .el-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  margin-left: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  height: 3rem;
  width: 6rem;
}

.comment-area-button .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 评论列表样式 */
.comments-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-thread {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-thread:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.reply-input-wrapper {
  margin-left: 60px;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 15px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-input-container {
  background: rgba(255, 255, 255, 0.08);
  padding: 15px;
}

.reply-input-container .comment-area-input {
  flex-grow: 1;
  margin-right: 1rem;
}

.reply-input-container .reply-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.reply-buttons .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reply-buttons .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.reply-buttons .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.replies-container {
  margin-left: 60px;
  margin-top: 1.2rem;
  padding-left: 20px;
  border-left: 3px solid rgba(102, 126, 234, 0.3);
}

.reply-block {
  margin-top: 1rem;
}

.reply-block .comment-container {
  background: rgba(102, 126, 234, 0.05);
  padding: 15px;
}

.reply-block .comment-area-avatar :deep(.el-avatar) {
  width: 40px !important;
  height: 40px !important;
}

.reply-to-text {
  font-size: 0.9em;
  color: #405783;
  margin-left: 8px;
}

.no-comments {
  text-align: center;
  color: #405783;
  padding: 3rem 0;
  font-size: 16px;
}

/* 多级回复样式 */
.second-level-replies {
  margin-left: 20px;
  padding-left: 20px;
  border-left: 3px solid rgba(118, 75, 162, 0.3);
  margin-top: 0.8rem;
}

.reply-to-reply-input-wrapper-level-3 {
  margin-left: 100px;
  margin-top: 0.5rem;
}

.third-level-replies {
  margin-left: 20px;
  padding-left: 20px;
  border-left: 3px solid rgba(255, 255, 255, 0.1);
  margin-top: 0.8rem;
}

.reply-to-reply-block-level-3 .comment-area-avatar :deep(.el-avatar) {
  width: 25px !important;
  height: 25px !important;
}

.reply-to-reply-block-level-3 .comment-details {
  font-size: 0.9em;
}

/* 确保各层级头像大小 */
.first-level-replies .comment-area-avatar :deep(.el-avatar) {
  width: 40px !important;
  height: 40px !important;
}

.second-level-replies .comment-area-avatar :deep(.el-avatar) {
  width: 30px !important;
  height: 30px !important;
}

.third-level-replies .comment-area-avatar :deep(.el-avatar) {
  width: 25px !important;
  height: 25px !important;
}
</style>
