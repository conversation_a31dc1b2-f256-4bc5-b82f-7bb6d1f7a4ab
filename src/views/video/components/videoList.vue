<script setup lang="ts">
import { useRouter } from "vue-router"
import { PropType } from "vue"
const router = useRouter()
const props = defineProps({
  relatedVideos: {
    type: Array as PropType<any[]>,
    required: true
  },
  isDetailPage: {
    type: Boolean,
    required: true,
    default: true
  }
})

// 跳转至其他视频详情页
const goToRecommendVideoDetail = (video: { id: number; title: string }) => {
  router.push({
    path: "/video/detail",
    query: { title: video.title }
  })
}
</script>

<template>
  <!-- 相关推荐区域 -->
  <div class="related-videos-section" v-if="relatedVideos.length">
    <el-row :gutter="21" v-if="isDetailPage">
      <el-col
        v-for="video in relatedVideos"
        :key="video.id"
        :xs="12"
        :sm="8"
        :md="6"
        :lg="6"
        :xl="6"
        class="video-List-col"
      >
        <div class="video-container" @click="goToRecommendVideoDetail(video)">
          <div>
            <el-image
              class="video-image"
              fit="cover"
              :src="video.coverImageUrl"
              :alt="video.title"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon size="20px"><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <div>
            <h3 class="video-title">{{ video.title }}</h3>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row v-else>
      <el-col
        v-for="video in relatedVideos"
        :key="video.id"
        :span="24"
        class="video-List-col-player"
      >
        <div
          class="video-container-player"
          @click="goToRecommendVideoDetail(video)"
        >
          <div>
            <el-image
              class="video-image-player"
              fit="cover"
              :src="video.coverImageUrl"
              :alt="video.title"
            >
              <template #error>
                <div class="image-slot-player">
                  <el-icon size="10px"><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <div class="video-title-player-container">
            <h3 class="video-title-player">{{ video.title }}</h3>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.video-List-col {
  margin-bottom: 24px;
}

.video-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.video-image {
  border-radius: 6px;
  cursor: pointer;
  height: 10rem;
  width: 100%;
  box-shadow:
    rgba(60, 64, 67, 0.3) 0px 1px 2px 0px,
    rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.video-image:hover {
  transform: translateY(-4px);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.24);
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 30px;
}

.video-title {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: #303030;
}
.video-title:hover {
  color: #bfc5db;
  cursor: pointer;
}

.video-List-col-player {
  margin-bottom: 1px;
}

.video-container-player {
  display: flex;
}

.video-title-player-container {
  margin-left: 10px;
  margin-top: 2px;
}

.video-image-player {
  border-radius: 6px;
  cursor: pointer;
  height: 6rem;
  width: 10rem;
}
.image-slot-player {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}

.video-title-player {
  font-size: 0.8rem;
  color: #405783;
  background: linear-gradient(90deg, rgba(42, 123, 155, 1) 0%, rgba(87, 199, 133, 1) 50%, rgba(83, 124, 237, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.video-title-player:hover {
  color: #b9c7f9;
  cursor: pointer;
}
</style>
