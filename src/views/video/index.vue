<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useRouter, useRoute } from "vue-router"
// import { useRenderIcon } from "@/components/ReIcon/src/hooks" // Removed as not used
// import VideoPlay from "~icons/ep/video-play" // Removed as not used
import VideoCategoryTags from "./components/VideoCategoryTags.vue"
import {
  getVideoInfoWithPageApi,
  getAllVideoTypeApi,
  getVideoInfoWithTypeApi
} from "@/api/video"
import { Picture as IconPicture } from "@element-plus/icons-vue"
defineOptions({
  name: "VideoHomePage"
})

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const totalItems = ref(0)
const totalPages = ref(0)
const keyword = ref("")
const selectedCategory = ref("")
const videoList = ref([])
const videoTypes = ref([])
const isShowCarousel = ref(true)


// 加载视频分类
const loadVideoTypes = async () => {
  try {
    const res = await getAllVideoTypeApi()
    if (res.success && res.data?.list) {
      videoTypes.value = res.data.list
    }
  } catch (err) {
    console.error("Failed to load video types:", err)
  }
}

// 加载视频列表，参数没有赋值才调用默认赋值参数
const loadVideos = async (page = 1, category = "") => {
  loading.value = true
  try {
    let res
    // 如果category有值，则按分类搜索，否则按关键词搜索
    const effectiveKeyword = category ? category : keyword.value
    const apiCall = category
      ? getVideoInfoWithTypeApi({
          keyword: effectiveKeyword,
          pageNum: page,
          pageSize: pageSize.value
        })
      : getVideoInfoWithPageApi({
          keyword: effectiveKeyword,
          pageNum: page,
          pageSize: pageSize.value
        })

    res = await apiCall

    if (res.success && res.data?.list) {
      videoList.value = res.data.list
      if (res.data.pageResult) {
        totalItems.value = res.data.pageResult.total
        totalPages.value = res.data.pageResult.pages
        currentPage.value = res.data.pageResult.pageNum
      }
    }
  } catch (err) {
    console.error("Failed to load videos:", err)
  } finally {
    loading.value = false
  }
}

// 处理分类选择事件
const handleCategorySelection = (categoryName: string) => {
  selectedCategory.value = categoryName
  isShowCarousel.value = false
  keyword.value = "" // 清空搜索关键词，因为我们是按分类搜索
  loadVideos(1, categoryName) // 使用新的分类名加载视频
}

// 搜索视频
const searchVideos = () => {
  isShowCarousel.value = false
  selectedCategory.value = "" // 清空分类选择，因为我们是按关键词搜索
  loadVideos(1) // 使用关键词加载视频
}

// 分页切换
const handlePageChange = page => {
  loadVideos(page, selectedCategory.value)
}

// 跳转到视频详情页
const goToVideoDetail = video => {
  router.push({
    path: "/video/detail",
    query: { title: video.title }
  })
}

onMounted(() => {
  loadVideoTypes() // 首先加载视频分类

  // 检查路由中是否带有 category 查询参数
  const categoryFromQuery = route.query.category as string
  if (categoryFromQuery) {
    // 如果有 category 参数，则调用 handleCategorySelection 处理分类选择
    handleCategorySelection(categoryFromQuery)
  } else {
    // 如果没有 category 参数，则加载所有视频 (初始加载)
    loadVideos()
  }
})
</script>

<template>
  <el-row class="video-home-mainPage">
    <el-col :span="4"></el-col>
    <el-col :span="16">
      <div class="video-home-page">
        <!-- 搜索区域 -->
        <div class="hero-section">
          <div class="hero-content">
            <h1 class="hero-title">发现、观看、分享</h1>
            <p class="hero-subtitle">探索数千部精彩影片，开启你的视觉之旅。</p>
            <el-input
              v-model="keyword"
              placeholder="搜索影片、动画..."
              class="hero-search-input"
              size="large"
              clearable
              @keyup.enter="searchVideos"
            >
              <template #prefix>
                <el-icon><i-ep-search /></el-icon>
              </template>
              <template #append>
                <el-button @click="searchVideos">搜索</el-button>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 分类选择区 -->
        <div class="category-section">
          <div class="category-section-title">
            <div class="section-title">分类</div>
          </div>

          <!-- 影片分类选择 -->
          <!--@cate监听触发方法,并接收子组件传入的参数,只要:传递值是是实时的，只要发生变化立刻改变-->
          <!--selectedCategory的值拿来触发标签class高亮-->
          <VideoCategoryTags
            :categories="videoTypes"
            :activeCategory="selectedCategory"
            @category-selected="handleCategorySelection"
            :interactive="true"
            :show-all-tag="true"
            all-tag-text="全部"
            all-tag-value=""
          />
        </div>

        <!-- 轮播推荐区 -->
        <div class="carousel-section" v-if="isShowCarousel&&videoList.length > 0">
          <el-carousel height="300px" motion-blur type="card">
            <el-carousel-item
              v-for="(video, index) in videoList.slice(0, 6)"
              :key="index"
            >
              <div
                class="carousel-item"
                :style="{ backgroundImage: `url(${video.coverImageUrl})` }"
                @click="goToVideoDetail(video)"
              >
                <div class="carousel-content">
                  <h2>{{ video.title }}</h2>
                  <!--<p>{{ video.description }}</p>-->
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>

        <!-- 视频列表区 -->
        <div class="video-list-section">
          <div class="section-title">
            {{ selectedCategory || keyword ? "搜索结果" : "所有影片" }}
          </div>
          <el-row :gutter="21" v-loading="loading">
            <el-col
              v-for="video in videoList"
              :key="video.id"
              :xs="12"
              :sm="12"
              :md="8"
              :lg="8"
              :xl="4"
              class="video-List-col"
            >
              <div class="video-container" @click="goToVideoDetail(video)">
                <div>
                  <el-image
                    class="video-image"
                    fit="cover"
                    :src="video.coverImageUrl"
                    :alt="video.title"
                  >
                    <template #error>
                      <div class="image-slot">
                        <el-icon size="40px"><icon-picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div>
                  <h3 class="video-title">{{ video.title }}</h3>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 分页 -->
          <div class="pagination-container" v-if="totalPages > 1">
            <el-pagination
              layout="prev, pager, next"
              :total="totalItems"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </el-col>
    <el-col :offset="4"></el-col>
  </el-row>
</template>

<style scoped>
.video-home-mainPage {
  background-color: #fff;
}

/* Add this style to make all direct children take the full width */
.video-home-page > * {
  width: 100%;
}

/* Hero Section */
.hero-section {
  padding: 80px 32px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  margin-bottom: 32px;
}

.hero-title {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 16px;
}

.hero-subtitle {
  font-size: 18px;
  font-weight: 300;
  opacity: 0.9;
  margin: 0 0 40px;
}

.hero-search-input {
  max-width: 600px;
  margin: 0 auto;
}
.hero-search-input :deep(.el-input-group__append) {
  background-color: var(--brand-color);
  border-color: var(--brand-color);
  color: #fff;
  box-shadow: none;
}
.hero-search-input :deep(.el-input__wrapper) {
  font-size: 16px;
}

.category-section {
  margin-bottom: 40px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.category-section-title {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  color: #1a202c;
  position: relative;
  padding-left: 20px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 80%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.carousel-section {
  margin-bottom: 40px;
}

.carousel-item {
  height: 100%;
  background-size: cover;
  background-position: top;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32px 24px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.carousel-content h2 {
  font-size: 32px;
  margin-bottom: 8px;
}

.video-list-section {
  margin-bottom: 40px;
}

.video-List-col {
  margin-bottom: 24px;
}

.video-container {
  display: flex;
  flex-direction: column;
}
.video-image {
  height: 17rem;
  width: 100%;
  border-radius: 6px;
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23);
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.video-image:hover {
  transform: translateY(-4px);
  box-shadow:
    0px -25px 20px -20px rgba(0, 0, 0, 0.45),
    25px 0 20px -20px rgba(0, 0, 0, 0.45),
    0px 25px 20px -20px rgba(0, 0, 0, 0.45),
    -25px 0 20px -20px rgba(0, 0, 0, 0.45);
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 30px;
}

.video-title {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: #303030;
}
.video-title:hover {
  color: #bfc5db;
  cursor: pointer;
}

.pagination-container {
  margin-top: 32px;
  display: flex;
  justify-content: center;
}
</style>
