<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, nextTick } from "vue"
import { useRoute, useRouter } from "vue-router"
import { deviceDetection } from "@pureadmin/utils"
import VideoPlay from "~icons/ep/video-play"
import { Events } from "xgplayer"
import Player from "xgplayer"
import Mp4Plugin from "xgplayer-mp4"
import "xgplayer/dist/index.min.css"
import { useVideoStore } from "@/store/modules/video"
import {
  getVideoInfoWithEpisodesApi,
  getVideoInfoWithTypeApi
} from "@/api/video"
import {
  createVideoEpisodeLikeApi,
  getVideoEpisodeLikeApi
} from "@/api/videoInteraction"
import type {
  VideoInfo,
  VideoEpisodes,
  VideoType,
  VideoDetailData
} from "@/api/video"
import { ElMessage } from "element-plus"
import VideoList from "./components/videoList.vue"
import NotoV1BackArrow from "~icons/noto-v1/back-arrow"
import LogosGoogleHome from "~icons/logos/google-home"
import IconamoonLikeLight from "~icons/iconamoon/like-light"
import IconamoonLikeFill from "~icons/iconamoon/like-fill"
import FluentVideoClip20Regular from "~icons/fluent/video-clip-20-regular"
import CommentSection from "./components/CommentSection.vue"

defineOptions({
  name: "VideoPlayerPage"
})

const route = useRoute()
const router = useRouter()
const playerInstance = ref(null)
const videoStore = useVideoStore()
const loading = ref(true)
const videoInfo = ref<VideoDetailData | null>(null)

// 存储当前集数以及信息
const currentEpisode = ref<VideoEpisodes | null>(null)
const relatedVideos = ref<VideoInfo[]>([])
const playerInitialized = ref(false)

// 获取路由参数
const videoId = ref(Number(route.query.videoId) || 0) // Number把值转为数字类型保证类型正确
const videoTitle = ref(
  typeof route.query.title === "string" ? route.query.title : ""
)
const episodeId = ref(Number(route.query.episodeId) || 0)
const episodeNumber = ref<number | string>(
  Number(route.query.episodeNumber) || 1
)

// 获取上次视频播放的进度
const initialStartTime = videoStore.getProgressByVideoId(videoId.value).value

// 播放器配置
const config = ref({
  id: "mse",
  lang: "zh",
  volume: 0.6,
  autoplay: false,
  width: "100%",
  height: "100%",
  playsinline: true,
  startTime: initialStartTime,
  videoAttributes: {
    crossOrigin: "anonymous"
  },
  url: "",
  progressDot: [
    {
      time: 181,
      text: "skip opening theme song"
    },
    {
      time: 300,
      text: "skip randomTime"
    },
    {
      time: 1360,
      text: "skip ending theme song"
    }
  ],
  plugins: [Mp4Plugin],
  mp4plugin: {
    maxBufferLength: 30,
    minBufferLength: 10,
    retryCount: 3,
    reqOptions: {
      mode: "cors",
      method: "GET",
      headers: {}
    }
  },
  pip: true,
  screenShot: true,
  fluid: deviceDetection(),
  playbackRate: [0.5, 0.75, 1, 1.5, 2],
  thumbnail: {
                    pic_num: 23*23,
                    width: 160,
                    height: 90,
                    col: 23,
                    row: 23,
                    urls: [""]
  }
})

// 获取视频详情和剧集信息
const getVideoDetail = async () => {
  try {
    if (!videoTitle.value) {
      router.push("/video/index")
      return
    }

    const res = await getVideoInfoWithEpisodesApi(videoTitle.value)
    if (res.success && res.data) {
      videoInfo.value = res.data

      // 用接收的集数id来查找到当前集数的对象
      if (episodeId.value) {
        currentEpisode.value = res.data.videoEpisodes.find(
          ep => ep.id === episodeId.value
        )
      } else {
        currentEpisode.value = res.data.videoEpisodes[0]
        episodeId.value = currentEpisode.value.id
      }

      // 查找到正确的集数后设置播放器的视频URL到配置里
      if (currentEpisode.value && currentEpisode.value.playUrl) {
        config.value.url = currentEpisode.value.playUrl,
        config.value.thumbnail.urls = currentEpisode.value.spriteSheetUrl
      }

      // 初始化播放器
      nextTick(() => {
        initPlayer()
      })

      // 获取相关视频推荐
      getRelatedVideos()
    }
  } catch (error) {
    console.error("获取视频详情失败:", error)
  } finally {
    loading.value = false
  }
}

// 获取相关视频推荐
const getRelatedVideos = async () => {
  const randomIndex = Math.floor(
    Math.random() * videoInfo.value.videoTypes.length
  )
  const randomType = videoInfo.value.videoTypes[randomIndex]
  const typeName = randomType.name
  try {
    const res = await getVideoInfoWithTypeApi({
      keyword: typeName,
      pageNum: 1,
      pageSize: 20
    })

    if (res.success && res.data?.list) {
      relatedVideos.value = res.data.list
        .filter(
          video =>
            video.id !== videoId.value && video.title !== videoTitle.value
        )
        .slice(0, 8)
    }
  } catch (error) {
    console.error("获取相关视频失败:", error)
  }
}

// 切换剧集
const switchEpisode = (episode: VideoEpisodes) => {
  if (playerInstance.value) {
    saveProgressToStore()

    playerInstance.value.destroy()
    playerInstance.value = null
    playerInitialized.value = false

    currentEpisode.value = episode // 更新当前集数信息，更新点赞数量和实现当前播放视频UI集数active状态
    config.value.url = episode.playUrl // 更新播放器视频URL,来实现视频的切换
    episodeId.value = episode.id // 更新集数id可以实现切换视频评论
    episodeNumber.value = episode.number
    config.value.thumbnail.urls = episode.spriteSheetUrl // 更新播放器缩略预览图

    // 更新路由参数,可以让用户切换集数后刷新页面或者分享页面为本集的URL路由参数(如果没更新刷新或分享只能播放第一集)
    router.replace({
      path: route.path,
      query: {
        videoId: videoId.value,
        title: videoTitle.value,
        episodeId: episode.id,
        episodeNumber: episode.number
      }
    })

    loading.value = true

    // 使用nextTick确保因为数据更新后的DOM元素重新渲染完成后再初始化播放器，不然有可能获得的数据为空或者更新前的数据
    nextTick(() => {
      initPlayer()
      loading.value = false
    })
  }
}

// 初始化播放器
const initPlayer = () => {
  if (playerInitialized.value) return

  // 初始化一个随机时间点
  initrandomTime()

  let player = new Player(config.value)
  playerInstance.value = player
  playerInitialized.value = true

  // 播放器播放视频和暂停还有播放结束的时候运行参数里对应的方法
  player.on(Events.TIME_UPDATE, throttledSaveProgress)
  player.on(Events.PAUSE, throttledSaveProgress)
  player.on(Events.ENDED, () => {
    clearProgressFromStore()

    // 播放结束自动切换到下一集
    const currentIndex = videoInfo.value.videoEpisodes.findIndex(
      ep => ep.id === currentEpisode.value.id
    )
    // 如果当前集数不是最后一集，则自动切换到下一集
    if (currentIndex < videoInfo.value.videoEpisodes.length - 1) {
      switchEpisode(videoInfo.value.videoEpisodes[currentIndex + 1])
    }
  })
}

function initrandomTime() {
  const randomTime = Math.floor(Math.random() * 1000)
  const foundIndex = config.value.progressDot.findIndex(item =>
    item.text.includes("randomTime")
  )
  if (foundIndex !== -1) {
    config.value.progressDot[foundIndex].time = randomTime
  }
}

// 保存当前播放进度到 Pinia store
const saveProgressToStore = () => {
  if (playerInstance.value && playerInstance.value.currentTime > 0) {
    videoStore.updateProgress(videoId.value, playerInstance.value.currentTime)
  }
}

// 从 Pinia store 清除播放进度
const clearProgressFromStore = () => {
  if (videoId.value) {
    videoStore.clearVideoProgress(videoId.value)
  }
}

// 节流函数
let throttleTimer = null
const throttledSaveProgress = () => {
  if (throttleTimer) return
  throttleTimer = setTimeout(() => {
    saveProgressToStore()
    throttleTimer = null
  }, 3000)
}

// 返回详情页
const goBack = () => {
  router.push({
    path: "/video/detail",
    query: { title: videoTitle.value }
  })
}

// 返回主页
const goHome = () => {
  router.push({
    path: "/video/index"
  })
}

// 点赞或取消点赞
const handleLike = async () => {
  const res = await createVideoEpisodeLikeApi(currentEpisode.value.id)

  if (res.success) {
    currentEpisode.value.status = !currentEpisode.value.status
    getLikeInfo()
    if (currentEpisode.value.status) {
      ElMessage({
      message: "点赞成功",
      type: "success",
      plain: true
    })
    } else {
      ElMessage({
      message: "取消点赞成功",
      type: "success",
      plain: true
    })
    }
    
  }
}

// 只有点赞后才用这个接口，获取点赞的最新数量
const getLikeInfo = async () => {
  const res = await getVideoEpisodeLikeApi(currentEpisode.value.id)
  if (res.success) {
    currentEpisode.value.likesCount = res.data.likesCount
  }
}

onMounted(async () => {
  await getVideoDetail()
})

onBeforeUnmount(() => {
  if (playerInstance.value) {
    saveProgressToStore()

    playerInstance.value.off(Events.TIME_UPDATE, throttledSaveProgress)
    playerInstance.value.off(Events.PAUSE, throttledSaveProgress)
    playerInstance.value.off(Events.ENDED, clearProgressFromStore)

    playerInstance.value.destroy()
    playerInstance.value = null
  }

  if (throttleTimer) {
    clearTimeout(throttleTimer)
  }
})
</script>

<template>
  <el-row class="video-home-mainPage">
    <el-col :span="4"></el-col>
    <el-col :span="12">
      <!-- Left Column: Player and Video Info -->
      <div class="leftColumn-content">
        <!-- 播放器区域 -->
        <div id="mse" class="player-element"></div>
      </div>
      <div v-if="loading" class="loading-container">
        <div class="loader"></div>
      </div>

      <!-- 视频信息区域 -->
      <div class="video-content-area">
        <div class="video-content-area-title">
          <NotoV1BackArrow
            style="width: 28px; height: 28px"
            @click="goBack"
            class="back-button"
          ></NotoV1BackArrow>
          <h2 class="video-main-title">{{ videoTitle }}</h2>
          <LogosGoogleHome
            style="width: 26px; height: 26px"
            @click="goHome"
            class="home-button"
          ></LogosGoogleHome>
        </div>
        <div class="video-stats">
          <span>By:Admin </span>
          <FluentVideoClip20Regular style="width: 26px; height: 26px" />
          <span> 0 </span>
          <div class="video-stats-like" @click="handleLike">
            <IconamoonLikeLight
              v-if="
                currentEpisode?.status == false ||
                currentEpisode?.status == null
              "
              style="width: 26px; height: 26px"
            />
            <IconamoonLikeFill v-else style="width: 26px; height: 26px" />
          </div>
          <span>
            {{ currentEpisode?.likesCount || 0 }}
          </span>
        </div>
      </div>

      <!-- 评论区 -->
      <CommentSection v-if="episodeId" :episodeId="episodeId" />
    </el-col>
    <el-col :span="4">
      <!--Right Column: Sidebar and Video List-->
      <!-- 剧集选择侧边栏 -->
      <div class="episodes-sidebar" v-if="videoInfo && videoInfo.videoEpisodes">
        <h3 class="sidebar-title">剧集列表</h3>
        <div class="episodes-list">
          <div
            v-for="episode in videoInfo.videoEpisodes"
            :key="episode.id"
            class="episode-item"
            :class="{
              active: currentEpisode && episode.id === currentEpisode.id
            }"
            @click="switchEpisode(episode)"
          >
            <span class="episode-number"> 第{{ episode.number }}集 </span>
            <span
              class="episode-active-icon"
              v-if="currentEpisode && episode.id === currentEpisode.id"
            >
              <VideoPlay style="width: 23px; height: 23px" />
            </span>
          </div>
        </div>
      </div>

      <!-- 相关视频 -->
      <div class="related-videos">
        <VideoList :relatedVideos="relatedVideos" :isDetailPage="false" />
      </div>
    </el-col>
    <el-col :span="4"></el-col>
  </el-row>
</template>

<style scoped>
.video-home-mainPage {
  min-height: 100vh;
  background-color: #ffffff;
  padding-top: 40px;
}

.leftColumn-content {
  width: 100%;
  aspect-ratio: 16/9;
  border-radius: 1px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* 美化播放器控制栏 */
.player-element :deep(.xgplayer-controls) {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  padding: 15px 20px;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 5;
}

.loader {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s linear infinite;
}

.video-content-area {
  padding: 30px 30px 30px 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.video-content-area-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2rem;
}

.back-button {
  cursor: pointer;
}
.back-button:hover {
  transform: scale(1.2);
}
.home-button {
  cursor: pointer;
}
.home-button:hover {
  transform: scale(1.2);
}

.video-main-title {
  font-size: 1.7rem;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.video-stats {
  margin-top: 0.6rem;
  display: flex;
  gap: 0.5rem;
}
.video-stats-like {
}

.video-title {
  flex-grow: 1;
  font-size: 32px;
  font-weight: 500;
  margin: 0 23px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.episodes-sidebar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  width: 100%;
  height: 27rem;
  border-radius: 0px 13px 13px 0px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 520px;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  font-size: 20px;
  font-weight: 600;
  padding: 25px;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  background: rgba(102, 126, 234, 0.2);
}

.episodes-list {
  padding: 15px;
  overflow-y: auto;
  flex: 1;
}

.episodes-list::-webkit-scrollbar {
  width: 8px;
}

.episodes-list::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.episodes-list::-webkit-scrollbar-thumb {
  background-color: rgba(102, 126, 234, 0.5);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.episodes-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(102, 126, 234, 0.8);
}

.episode-item {
  padding: 15px 20px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
}

.episode-item:hover {
  background-color: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  transform: translateX(5px);
  color: white;
}

.episode-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
  transform: scale(1.02);
}

.episode-number {
  font-size: 16px;
  font-weight: 600;
  color: #405783;
}

.episode-active-icon {
  color: white;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
}
.related-videos {
  background: rgba(255, 255, 255, 0.06);
  margin-top: 0.2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 0px 13px 13px 0px;
}
</style>
