<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import {
  getVideoInfoWithEpisodesApi,
  getVideoInfoWithPageApi,
  getVideoInfoWithTypeApi
} from "@/api/video"
import VideoList from "./components/videoList.vue"
import VideoCategoryTags from "./components/VideoCategoryTags.vue"

defineOptions({
  name: "VideoDetailPage"
})

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const videoInfo = ref(null)
const relatedVideos = ref([])

// 获取视频详情和剧集信息
const getVideoDetail = async () => {
  try {
    const title = route.query.title as string
    if (!title) {
      router.push("/video/index")
      return
    }

    const res = await getVideoInfoWithEpisodesApi(title)
    if (res.success && res.data) {
      videoInfo.value = res.data
      getRelatedVideos()
    }
  } catch (error) {
    console.error("Failed to fetch video detail:", error)
  } finally {
    loading.value = false
  }
}

// 获取相关视频（这里暂时用默认视频列表代替，后续可根据需求改进）
const getRelatedVideos = async () => {
  // 生成随机数的索引
  const randomIndex = Math.floor(
    Math.random() * videoInfo.value.videoTypes.length
  )
  // 使用随机算法获取关于视频类型的值
  const randomType = videoInfo.value.videoTypes[randomIndex]
  const typeName = randomType.name
  try {
    const res = await getVideoInfoWithTypeApi({
      keyword: typeName,
      pageNum: 1,
      pageSize: 13
    })

    if (res.success && res.data?.list) {
      // 过滤掉当前视频（通过ID和标题）
      relatedVideos.value = res.data.list
        .filter(
          video =>
            video.id !== videoInfo.value.id &&
            video.title !== videoInfo.value.title
        )
        .slice(0, 13)
    }
  } catch (error) {
    console.error("Failed to fetch related videos:", error)
  }
}

// 前往播放器页面
const goToPlayer = (episode: {
  id: number
  number: number
  playUrl: string
}) => {
  router.push({
    path: "/video/player",
    query: {
      videoId: videoInfo.value.id,
      title: videoInfo.value.title,
      episodeId: episode.id,
      episodeNumber: episode.number
    }
  })
}

// 点击分类标签导航到首页并按分类搜索
const navigateToCategorySearch = (categoryName: string) => {
  router.push({
    path: "/video/index", // 目标路径为视频首页
    query: { category: categoryName } // 将分类名作为查询参数
  })
}

onMounted(() => {
  getVideoDetail()
})
</script>

<template>
  <el-row class="video-home-mainPage">
    <el-col :span="4"></el-col>
    <el-col :span="16">
      <div class="video-detail-page">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <template v-else-if="videoInfo">
          <!-- 视频信息区域 -->
          <div class="video-info-section">
            <div class="video-poster">
              <img :src="videoInfo.coverImageUrl" :alt="videoInfo.title" />
            </div>
            <div class="video-info">
              <h2 class="video-title">{{ videoInfo.title }}</h2>
              <div
                class="video-types-container"
                v-if="videoInfo.videoTypes && videoInfo.videoTypes.length"
              >
                <!-- 
                  VideoCategoryTags 组件用于显示视频分类标签。
                  - :categories="videoInfo.videoTypes": 传递视频分类数据。
                  - :interactive="true": 设置为可交互，以便标签可以被点击。
                  - activeCategory="": 在此详情页，activeCategory 不用于高亮特定分类，传空字符串以满足组件 prop 的要求。
                  - @category-selected="navigateToCategorySearch": 监听子组件派发的 category-selected 事件，并调用 navigateToCategorySearch 方法进行导航。
                -->
                <VideoCategoryTags
                  :categories="videoInfo.videoTypes"
                  :interactive="true"
                  activeCategory=""
                  @category-selected="navigateToCategorySearch"
                />
              </div>

              <div class="video-description">
                {{ videoInfo.description }}
              </div>
            </div>
          </div>

          <!-- 剧集选择区域 -->
          <div class="episodes-section">
            <div class="episodes-container">
              <el-row :gutter="20">
                <el-col
                  v-for="episode in videoInfo.videoEpisodes"
                  :key="episode.id"
                  :xs="12"
                  :sm="6"
                  :md="4"
                  :lg="3"
                  :xl="2"
                >
                  <el-button
                    class="episode-button"
                    @click="goToPlayer(episode)"
                    block
                  >
                    {{ episode.number }}
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>

          <div class="related-videos">
          <!-- 相关推荐区域 -->
          <VideoList :relatedVideos="relatedVideos" :isDetailPage="true" />
          </div>
        </template>

        <div v-else class="not-found">
          <h2>影片未找到</h2>
          <el-button type="primary" @click="router.push('/video/index')">
            返回首页
          </el-button>
        </div>
      </div>
    </el-col>
    <el-col :offset="4"></el-col>
  </el-row>
</template>

<style scoped>
.video-home-mainPage {
  min-height: 100vh;
  padding: 20px 0;
  background-color: #ffffff;
}

.video-detail-page {
  margin: 0 auto;
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-container {
  padding: 60px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin: 20px;
}

.video-info-section {
  display: flex;
  margin-bottom: 50px;
  gap: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.video-poster {
  flex: 0 0 320px;
  height: 480px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
  transition: transform 0.3s ease;
}

.video-poster:hover {
  transform: scale(1.02);
}

.video-poster::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  pointer-events: none;
}

.video-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-poster:hover img {
  transform: scale(1.05);
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 20px;
  line-height: 1.2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.video-types-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 30px 0;
}

.video-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a5568;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.episodes-section {
  margin-bottom: 50px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.episodes-section h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 25px;
  position: relative;
  padding-left: 20px;
}

.episodes-section h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 70%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.episodes-container {
  padding: 10px 0;
}

.episode-button {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: white;
  color: #4a5568;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.episode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: left 0.3s ease;
  z-index: 0;
}

.episode-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  color: white;
  border-color: transparent;
}

.episode-button:hover::before {
  left: 0;
}

.related-videos {
  padding: 3rem 3rem 0rem 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.episode-button span {
  position: relative;
  z-index: 1;
}

.episode-button:active {
  transform: translateY(-1px);
}

/* 当前播放的剧集样式 */
.episode-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.not-found {
  text-align: center;
  padding: 100px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin: 20px;
}

.not-found h2 {
  font-size: 2rem;
  color: #4a5568;
  margin-bottom: 30px;
}

.not-found .el-button {
  height: 50px;
  padding: 0 40px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.not-found .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

/* 美化骨架屏 */
.loading-container :deep(.el-skeleton) {
  padding: 20px;
}

.loading-container :deep(.el-skeleton__item) {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (max-width: 1024px) {
  .video-info-section {
    padding: 30px;
    gap: 30px;
  }
  
  .video-poster {
    flex: 0 0 280px;
    height: 420px;
  }
  
  .video-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .video-home-mainPage {
    padding: 20px 0;
  }
  
  .video-info-section {
    flex-direction: column;
    padding: 20px;
    gap: 20px;
  }

  .video-poster {
    flex: 0 0 auto;
    height: auto;
    aspect-ratio: 2/3;
    margin: 0 auto;
    max-width: 300px;
  }

  .video-title {
    font-size: 1.8rem;
    text-align: center;
  }
  
  .video-description {
    font-size: 1rem;
    padding: 15px;
  }
  
  .episodes-section {
    padding: 20px;
  }
  
  .episode-button {
    height: 45px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .video-info-section {
    padding: 15px;
    border-radius: 15px;
  }
  
  .video-title {
    font-size: 1.5rem;
  }
  
  .episodes-section h3 {
    font-size: 1.4rem;
  }
  
  .episode-button {
    height: 40px;
    font-size: 13px;
  }
}
</style>
