<!--
  RecaptchaWidget.vue
  一个可复用的 Vue 3 reCAPTCHA v2 组件
-->
<template>
  <!-- 这是一个空的 div，将作为 reCAPTCHA 微件的渲染容器。 -->
  <!-- 我们使用 ref 来获取它的 DOM 引用，以便传递给 reCAPTCHA 的 render 方法。 -->
  <div ref="recaptchaContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

// --- 1. 定义组件的接口 (Props, Emits, Expose) ---

/**
 * @property {string} sitekey - 【必须】你的 reCAPTCHA v2 站点密钥 (Site Key)。
 */
const props = defineProps({
  sitekey: {
    type: String,
    required: true,
  },
});

/**
 * 定义组件可以发出的事件，用于与父组件通信。
 * @event verify - 当用户成功完成验证时触发，并携带一个 `token` 字符串。
 * @event expired - 当验证 `token` 过期时触发。
 * @event error - 当 reCAPTCHA 加载或渲染失败时触发。
 */
const emit = defineEmits(['verify', 'expired', 'error']);

/**
 * 使用 defineExpose 暴露一个 `reset` 方法，允许父组件通过 ref 命令式地重置此组件。
 * 例如：<RecaptchaWidget ref="recaptchaRef" /> ... recaptchaRef.value.reset()
 */
defineExpose({
  reset: resetWidget,
});

// --- 2. 内部状态和引用 ---

// 用于引用模板中的 <div> 容器元素。
const recaptchaContainer = ref<HTMLElement | null>(null);

// 用于存储 reCAPTCHA 的 `render` 方法返回的 widget ID。
// 这个 ID 在调用 `reset` 等 API 时会用到。
const widgetId = ref<number | null>(null);


// --- 3. 核心功能方法 ---

/**
 * 渲染 reCAPTCHA 微件的核心函数。
 */
function renderRecaptchaWidget() {
  // 下载外部script后并运行后，绘制微件
  // 健壮性检查：确保 reCAPTCHA 的 API 已经准备就绪，并且我们的容器也已挂载。
  if (window.grecaptcha && window.grecaptcha.ready && recaptchaContainer.value) {
    // grecaptcha.ready() 是官方推荐的方式，它能确保 API 完全初始化后再执行内部代码。
    window.grecaptcha.ready(() => {
      try {
        // 调用 reCAPTCHA 的 render 方法来绘制微件。
        widgetId.value = window.grecaptcha.render(recaptchaContainer.value, {
          'sitekey': props.sitekey,
          // 【回调】验证成功时，执行此函数。
          'callback': (token: string) => {
            // 通过 'verify' 事件将获取到的 token 发送给父组件。
            emit('verify', token);
          },
          // 【回调】验证过期时，执行此函数。
          'expired-callback': () => {
            // 通过 'expired' 事件通知父组件。
            emit('expired');
          },
          // 【回调】发生错误时（如网络问题），执行此函数。
          'error-callback': () => {
            emit('error');
          },
        });
      } catch (error) {
        console.error('Failed to render reCAPTCHA:', error);
        emit('error');
      }
    });
  }
}

/**
 * 重置 reCAPTCHA 微件，使其回到未验证状态。
 * 这个方法通过 defineExpose 暴露给了父组件。
 */
function resetWidget() {
  // 确保 API 和 widgetId 都存在。
  if (window.grecaptcha && widgetId.value !== null) {
    window.grecaptcha.reset(widgetId.value);
  }
}

// --- 4. 生命周期钩子 ---

/**
 * 在组件被挂载到 DOM 后执行。
 * 这是加载第三方脚本和进行初始化的理想位置。
 */
onMounted(() => {
  // 检查 reCAPTCHA 脚本是否已经被加载，以防止在应用中有多个实例时重复加载，如script.src未下载会出现undfined
  if (window.isRecaptchaScriptLoaded) {
    // 如果脚本已加载，直接尝试渲染微件。
    renderRecaptchaWidget();
    return;
  }

  // 如果脚本未加载(第一次进页面的时候)，则动态创建 <script> 标签并注入到页面中。
  const script = document.createElement('script');
  
  // 【关键】URL 中必须包含 `render=explicit` 参数，
  // 这告诉 reCAPTCHA API 不要自动渲染，而是等待我们手动调用 `grecaptcha.render()`。
  // 从浏览器多线程异步下载外部的api.js文件
  script.src = 'https://www.recaptcha.net/recaptcha/api.js?render=explicit';
  script.async = true;
  script.defer = true;

  // 当脚本成功下载然后加载并执行后，这个 onload 事件会被触发。
  script.onload = () => {
    // 设置一个全局标记，表示脚本已加载。
    window.isRecaptchaScriptLoaded = true;
    // 现在可以安全地渲染微件了。
    renderRecaptchaWidget();
  };

  // 如果脚本加载失败，触发 error 事件。
  script.onerror = () => {
    console.error('Failed to load the reCAPTCHA script.');
    emit('error');
  }

  // 将脚本添加到文档的 <head> 中，开始加载。
  document.head.appendChild(script);
});

/**
 * 组件被卸载时执行。
 * 在这里可以进行一些清理工作。
 */
onUnmounted(() => {
  // 虽然 reCAPTCHA 没有提供一个直接的“销毁”方法来清理 widget，
  // 但如果需要，可以在这里移除相关 DOM 或取消监听器。
  // 对于 reCAPTCHA，通常不需要特别的清理操作。
});


// --- 5. TypeScript 类型声明 ---

/**
 * 由于我们在 window 对象上附加了自定义属性（grecaptcha, isRecaptchaScriptLoaded），
 * 需要在这里使用 `declare global` 来扩展 Window 接口的类型定义，
 * 这样 TypeScript 编译器就不会因为找不到这些属性而报错。
 */
declare global {
  interface Window {
    // reCAPTCHA API 挂载在 window 上的对象。
    grecaptcha: any;
    // 我们自定义的全局标记，用于防止脚本重复加载。
    isRecaptchaScriptLoaded?: boolean;
  }
}
</script>