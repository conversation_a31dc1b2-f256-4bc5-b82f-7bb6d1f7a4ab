import { ref } from "vue"
import { sendEmailCode } from "@/api/user"
import { message } from "@/utils/message"
import { REGEXP_EMAIL } from "./rule" // Ensure this path is correct
import { $t, transformI18n } from "@/plugins/i18n"

const isDisabled = ref(false)
let timer = ref()
let initTime = ref(60)
const text = ref("")

export const useVerifyCode = () => {
  const start = async (email: string) => {
    // 1. Check if email is provided
    if (!email) {
      message(transformI18n($t("login.pureEmailReg")), { type: "warning" })
      return
    }

    // 2. Check email format using regex
    if (!REGEXP_EMAIL.test(email)) {
      message(transformI18n($t("login.pureEmailCorrectReg")), {
        type: "warning"
      })
      return
    }

    // --- Email is valid, proceed to send code ---
    try {
      await sendEmailCode(email) // Use the correct API function
      message(transformI18n($t("login.sendCodeSuccess")), { type: "success" })
      isDisabled.value = true
      text.value = `${initTime.value}秒后重新获取`
      clearInterval(timer.value) // Clear any existing timer
      timer.value = setInterval(() => {
        initTime.value--
        text.value = `${initTime.value}秒后重新获取`
        if (initTime.value <= 0) {
          initTime.value = 60 // Reset time
          end() // Call end to clear interval and reset state
        }
      }, 1000)
    } catch (apiError) {
      message(transformI18n($t("login.sendCodeError")), { type: "error" })
      console.error("Failed to send email verification code:", apiError)
      end() // Ensure button is re-enabled on API error
    }
  }

  const end = () => {
    text.value = ""
    isDisabled.value = false
    clearInterval(timer.value)
    initTime.value = 60 // Reset timer duration
  }

  // Return the reactive refs and functions
  return {
    isDisabled,
    timer,
    text,
    start,
    end
  }
}
