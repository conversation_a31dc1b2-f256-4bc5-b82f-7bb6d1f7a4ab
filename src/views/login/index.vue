<script setup lang="ts">
import { useI18n } from "vue-i18n"
import Motion from "./utils/motion"
import { useRouter } from "vue-router"
import { message } from "@/utils/message"
import { loginRules } from "./utils/rule"
import TypeIt from "@/components/ReTypeit"
import { debounce } from "@pureadmin/utils"
import { useNav } from "@/layout/hooks/useNav"
import { useEventListener } from "@vueuse/core"
import type { FormInstance } from "element-plus"
import { $t, transformI18n } from "@/plugins/i18n"
import { operates, thirdParty } from "./utils/enums"
import { useLayout } from "@/layout/hooks/useLayout"
import LoginPhone from "./components/LoginPhone.vue"
import LoginRegist from "./components/LoginRegist.vue"
import LoginUpdate from "./components/LoginUpdate.vue"
import LoginQrCode from "./components/LoginQrCode.vue"
import { useUserStoreHook } from "@/store/modules/user"
import { initRouter, getTopMenu } from "@/router/utils"
import { bg, avatar, illustration } from "./utils/static"
import { ReImageVerify } from "@/components/ReImageVerify"
import { oauthRender } from "@/api/user"
import { ref, toRaw, reactive, watch, computed } from "vue"
import { useRenderIcon } from "@/components/ReIcon/src/hooks"
import { useTranslationLang } from "@/layout/hooks/useTranslationLang"
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange"
import { ElMessage } from 'element-plus'

import { useUserStore } from "@/store"
import LoginReCaptcha from "./components/LoginReCaptcha.vue"
import { useRoute } from "vue-router"

import dayIcon from "@/assets/svg/day.svg?component"
import darkIcon from "@/assets/svg/dark.svg?component"
import globalization from "@/assets/svg/globalization.svg?component"
import Lock from "~icons/ri/lock-fill"
import Check from "~icons/ep/check"
import User from "~icons/ri/user-3-fill"
import Info from "~icons/ri/information-line"
import Keyhole from "~icons/ri/shield-keyhole-line"

defineOptions({
  name: "Login"
})

const imgCode = ref("")
const loginDay = ref(7)
const router = useRouter()
const loading = ref(false)
const checked = ref(false)
const disabled = ref(false)
const ruleFormRef = ref<FormInstance>()
const currentPage = computed(() => {
  return useUserStoreHook().currentPage
})


const { t } = useI18n()
const { initStorage } = useLayout()
initStorage()
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange()
dataThemeChange(overallStyle.value)
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav()
const { locale, translationCh, translationEn } = useTranslationLang()

const ruleForm = reactive({
  username: "",
  password: "",
  //verifyCode: ""
})

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password,
          recaptchaToken:recaptchaToken.value
        })
        .then(res => {
          if (res.success) {
            // 获取后端路由
            console.log("登录成功，开始获取路由...")
            return initRouter()
              .then(() => {
                console.log("路由初始化成功，获取用户资料...")
                // 获取并设置用户资料
                return useUserStoreHook().fetchAndSetUserProfile()
              })
              .then(() => {
                console.log("用户资料获取成功，准备跳转...")
                disabled.value = true

                // 直接跳转到welcome页面，不依赖getTopMenu
                console.log("直接跳转到welcome页面")
                router.push("/welcome").catch(err => {
                  console.error("跳转到welcome失败:", err)
                  // 如果welcome页面不存在，尝试跳转到根路径
                  router.push("/").catch(rootErr => {
                    console.error("跳转到根路径也失败:", rootErr)
                    message("无法跳转到主页，请联系管理员", { type: "warning" })
                  })
                })

                message(t("login.pureLoginSuccess"), { type: "success" })
                disabled.value = false
              })
              .catch(error => {
                console.error("路由或用户资料获取失败:", error)
                message("登录成功但无法加载页面，请刷新重试", {
                  type: "warning"
                })
                // 尝试直接跳转到欢迎页
                router.push("/welcome").catch(() => {
                  router.push("/")
                })
              })
          } else {
            message(t("login.pureLoginFail"), { type: "error" })
          }
        })
        .catch(error => {
          console.error("登录请求失败:", error)
          message("登录失败，请检查网络连接", { type: "error" })
        })
        .finally(() => (loading.value = false))
    }
  })
}

function presistUsername() {
  let username = useUserStoreHook().username
  if (username == "") {
    username = "account"
  }
  ruleForm.username = username
}

const immediateDebounce: any = debounce(formRef => onLogin(formRef), 1000, true)

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value)
})
presistUsername()

/*
watch(imgCode, value => {
  useUserStoreHook().SET_VERIFYCODE(value)
})
  */
watch(checked, bool => {
  useUserStoreHook().SET_ISREMEMBERED(bool)
})
watch(loginDay, value => {
  useUserStoreHook().SET_LOGINDAY(value)
})

const clickableThirdParties = ["github", "gitee"]

const isClickableThirdParty = (name: string) => {
  return clickableThirdParties.includes(name)
}

const handleThirdPartyLogin = (name: string) => {
  if (isClickableThirdParty(name)) {
    oauthRender(name)
  }
}

const route = useRoute()
const getCurrentPage = () => {
  if (route.query.currentPage) {
    useUserStoreHook().SET_CURRENTPAGE(Number(route.query.currentPage))
  }
}
getCurrentPage()


// 网站toekn向服务商获取验证码token
const sitekey = ref<string>("6LenmWYrAAAAAFnPsNewt_8AO2C2PkhE_793VFPD")

// 验证码token传递后端，在后端使用token和后端持有的密钥一起向服务商验证这个token是否真的通过验证
const recaptchaToken = ref<string | null>(null)
const recaptchaComponent = ref<InstanceType<typeof LoginReCaptcha> | null>(null)

// 当 reCAPTCHA 验证成功时，这个方法会被调用
const onRecaptchaVerified = async (token: string) => {
  console.log("reCAPTCHA 验证成功! Token:", token)
  recaptchaToken.value = token;
  ElMessage({
    message: '验证成功！现在可以登录了。',
    type: 'success',
  })

}

// 当 reCAPTCHA 验证过期时，这个方法会被调用
const onRecaptchaExpired = () => {
  console.log("reCAPTCHA 已过期.")
  recaptchaToken.value = null
  ElMessage({
    message: '验证已过期，请重新验证。',
    type: 'warning',
  })
  // 重置验证组件
  recaptchaComponent.value?.reset()
}

const onRecaptchaError = () => {
  console.log("reCAPTCHA 错误")
  ElMessage({
    message: '验证发生错误，请重新验证',
    type: 'error',
  })
}
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <!-- 国际化 -->
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline
                v-show="locale === 'zh'"
                class="check-zh"
                :icon="Check"
              />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span v-show="locale === 'en'" class="check-en">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">

      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{ strings: [title], cursor: false, speed: 100 }"
              />
            </h2>
          </Motion>

          <el-form
            v-if="currentPage === 0"
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: transformI18n($t('login.pureUsernameReg')),
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  :placeholder="t('login.pureUsername')"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  :placeholder="t('login.purePassword')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>
            <!--
            <Motion :delay="200">
              <el-form-item prop="verifyCode">
                <el-input
                  v-model="ruleForm.verifyCode"
                  clearable
                  :placeholder="t('login.pureVerifyCode')"
                  :prefix-icon="useRenderIcon(Keyhole)"
                >
                  <template v-slot:append>
                    <ReImageVerify v-model:code="imgCode" />
                  </template>
                </el-input>
              </el-form-item>
            </Motion>
            -->

            <Motion :delay="250">
              <el-form-item>
                <div class="w-full h-[20px] flex justify-between items-center">
                  <el-checkbox v-model="checked">
                    <span class="flex">
                      <select
                        v-model="loginDay"
                        :style="{
                          width: loginDay < 10 ? '10px' : '16px',
                          outline: 'none',
                          background: 'none',
                          appearance: 'none'
                        }"
                      >
                        <option value="1">1</option>
                        <option value="7">7</option>
                        <option value="30">30</option>
                      </select>
                      {{ t("login.pureRemember") }}
                      <IconifyIconOffline
                        v-tippy="{
                          content: t('login.pureRememberInfo'),
                          placement: 'top'
                        }"
                        :icon="Info"
                        class="ml-1"
                      />
                    </span>
                  </el-checkbox>
                  <el-button
                    link
                    type="primary"
                    @click="useUserStoreHook().SET_CURRENTPAGE(4)"
                  >
                    {{ t("login.pureForget") }}
                  </el-button>
                </div>
                <el-button
                  class="w-full mt-4"
                  size="default"
                  type="primary"
                  :loading="loading"
                  :disabled="disabled || !recaptchaToken"
                  @click="onLogin(ruleFormRef)"
                >
                  {{ t("login.pureLogin") }}
                </el-button>
              </el-form-item>
            </Motion>

            <Motion :delay="300">
              <el-form-item>
                <div class="w-full h-[20px] flex justify-between items-center">
                  <el-button
                    v-for="(item, index) in operates"
                    :key="index"
                    class="w-full mt-4"
                    size="default"
                    @click="useUserStoreHook().SET_CURRENTPAGE(index + 1)"
                  >
                    {{ t(item.title) }}
                  </el-button>
                </div>
              </el-form-item>
            </Motion>
          </el-form>

          <!--第三方登录-->
          <Motion v-if="currentPage === 0" :delay="350">
            <el-form-item>
              <el-divider>
                <p class="text-gray-500 text-xs">
                  {{ t("login.pureThirdLogin") }}
                </p>
              </el-divider>
              <div class="w-full flex justify-evenly">
                <span
                  v-for="(item, index) in thirdParty"
                  :key="index"
                  :title="t(item.title)"
                  @click="handleThirdPartyLogin(item.name)"
                >
                  <IconifyIconOnline
                    :icon="
                      item.icon.includes(':')
                        ? item.icon
                        : `ri:${item.icon}-fill`
                    "
                    width="20"
                    :class="[
                      'text-gray-500',
                      isClickableThirdParty(item.name)
                        ? 'cursor-pointer hover:text-blue-400'
                        : 'cursor-not-allowed'
                    ]"
                  />
                </span>
              </div>
            </el-form-item>
          </Motion>
          <!-- 手机号登录 -->
          <LoginPhone v-if="currentPage === 1" />
          <!-- 二维码登录 -->
          <LoginQrCode v-if="currentPage === 2" />
          <!-- 注册 -->
          <LoginRegist v-if="currentPage === 3" />
          <!-- 忘记密码 -->
          <LoginUpdate v-if="currentPage === 4" />
          <!-- 第三方登录验证 -->
          <LoginReCaptcha
            ref="recaptchaComponent"
            :sitekey="sitekey"
            @verify="onRecaptchaVerified"
            @expired="onRecaptchaExpired"
            @error="onRecaptchaError"
          />
        </div>
      </div>
    </div>
    <div
      class="w-full flex-c absolute bottom-3 text-sm text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"
    >
      Copyright © 2020-present
      <a
        class="hover:text-primary"
        href="https://github.com/pure-admin"
        target="_blank"
      >
        &nbsp;{{ title }}
      </a>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}
</style>
