<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from "vue"
import { deviceDetection } from "@pureadmin/utils"
import { useRenderIcon } from "@/components/ReIcon/src/hooks"
import VideoPlay from "~icons/ep/video-play"
import { Events } from "xgplayer"
import Player from "xgplayer"
import Mp4Plugin from "xgplayer-mp4"
import "xgplayer/dist/index.min.css"
import { useVideoStore } from "@/store/modules/video"
import { getVideoInfoWithEpisodesApi } from "@/api/video"

defineOptions({
  name: "VideoPage"
})
const playerInstance = ref(null)
const videoStore = useVideoStore()
const videoId = ref(2)

// 获取上次视频播放的
const initialStartTime = videoStore.getProgressByVideoId(videoId.value).value

const config = ref({
  id: "mse",
  lang: "zh",
  // 默认静音
  volume: 0.6,
  autoplay: false,
  width: 1300,
  height: 700,
  playsinline: true,
  startTime: initialStartTime, //视频开始时间
  videoAttributes: {
    crossOrigin: "anonymous"
  },
  url: "",
  playnext: {
    urlList: ["./a.mp4"]
  },
  plugins: [Mp4Plugin],
  mp4plugin: {
    maxBufferLength: 30,
    minBufferLength: 10,
    retryCount: 3,
    reqOptions: {
      mode: "cors",
      method: "GET",
      headers: {
        // 'Authorization': 'Bearer ' + yourTokenVariable 假如要用JWT令牌
      }
    }
  },
  pip: true,
  screenShot: true,
  fluid: deviceDetection(),
  //传入倍速可选数组
  playbackRate: [0.5, 0.75, 1, 1.5, 2]
})

const getVideoInfoWithepisodes = async () => {
  const res = await getVideoInfoWithEpisodesApi("你与偶像光之美少女")
  console.log(res)
  config.value.url = res.data.videoEpisodes[1].playUrl
  console.log(config.value.url, "视频地址")
}
getVideoInfoWithepisodes()

// 保存当前播放进度到 Pinia store
const saveProgressToStore = () => {
  if (playerInstance.value && playerInstance.value.currentTime > 0) {
    videoStore.updateProgress(videoId.value, playerInstance.value.currentTime)
    console.log(
      `进度已通过 Pinia (Setup Store) 保存: ${playerInstance.value.currentTime}`
    )
  }
}

// 从 Pinia store 清除播放进度
const clearProgressFromStore = () => {
  if (videoId.value) {
    videoStore.clearVideoProgress(videoId.value)
    console.log("进度已通过 Pinia (Setup Store) 清除")
  }
}

// 节流函数
let throttleTimer = null
const throttledSaveProgress = () => {
  if (throttleTimer) return
  // 每秒保存一次延迟的1000毫秒到 throttleTimer,一秒后执行方法内的函数，只有一秒后执行完把计时器变量清空才能请求第二次
  throttleTimer = setTimeout(() => {
    saveProgressToStore()
    throttleTimer = null
  }, 3600)
}

onMounted(() => {
  console.log(
    "Initial startTime being passed to Player:",
    config.value.startTime
  )
  let player = new Player(config.value)
  playerInstance.value = player // 保存播放器实例

  // 播放时间改变时运行方法
  player.on(Events.TIME_UPDATE, () => {
    throttledSaveProgress()
  })
  // 暂停时运行方法
  player.on(Events.PAUSE, () => {
    throttledSaveProgress()
  })
  // 只有视频观看完全部进度时才运行方法
  player.on(Events.ENDED, () => {
    clearProgressFromStore()
  })
  console.log(playerInstance.value.startTime, "播放进度字节")
  // 资源加载时候加载清晰度
  player.emit("resourceReady", [
    { name: "超清", url: "http://localhost:8080/api/video/video/YouAndMe.mp4" },
    {
      name: "标准",
      url: "http://localhost:8p080/api/video/video/YouAndMe2.mp4"
    }
  ])
})

onBeforeUnmount(() => {
  console.log(
    playerInstance.value.currentTime,
    playerInstance.value.startTime,
    "时间"
  )
  if (playerInstance.value) {
    saveProgressToStore() // 退出前最后保存一次
    playerInstance.value.off(Events.TIME_UPDATE, () => {
      throttledSaveProgress()
    })
    playerInstance.value.off(Events.PAUSE, () => {
      throttledSaveProgress()
    })
    playerInstance.value.off(Events.ENDED, () => {
      clearProgressFromStore()
    })
    playerInstance.value.destroy()
    playerInstance.value = null
  }
  if (throttleTimer) {
    clearTimeout(throttleTimer)
  }
})
</script>

<template>
  <el-card shadow="never">
    <div id="mse" />
  </el-card>
</template>

<style scoped>
#mse {
  flex: auto;
}
</style>
