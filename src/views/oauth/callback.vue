<script setup lang="ts">
import { useRouter, useRoute } from "vue-router"
import { onMounted, ref } from "vue"
import { useUserStoreHook } from "@/store/modules/user"
import { message } from "@/utils/message"
import { initRouter, getTopMenu } from "@/router/utils"
import { useI18n } from "vue-i18n"

defineOptions({
  name: "OAuthCallback"
})

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const loading = ref(true)
const provider = ref((route.query.provider as string) || "第三方服务")

// 处理OAuth回调
const handleOAuthCallback = async () => {
  try {
    console.log("OAuth回调处理开始...")
    // 在这里调用refreshTokenApi，因为此时后端已经设置了refresh_token cookie
    const res = await useUserStoreHook().handRefreshToken()
    console.log("刷新token结果:", res)

    if (res && res.success) {
      // 获取后端路由
      console.log("开始初始化路由...")
      await initRouter()
      console.log("路由初始化完成")

      // 获取并设置用户资料
      console.log("获取用户资料...")
      await useUserStoreHook().fetchAndSetUserProfile()
      console.log("用户资料获取完成")

      // 直接跳转到welcome页面，不依赖getTopMenu
      console.log("直接跳转到welcome页面")
      router.push("/welcome").catch(err => {
        console.error("跳转到welcome失败:", err)
        // 如果welcome页面不存在，尝试跳转到根路径
        router.push("/").catch(rootErr => {
          console.error("跳转到根路径也失败:", rootErr)
          message("无法跳转到主页，请联系管理员", { type: "warning" })
        })
      })

      message(t("login.pureLoginSuccess"), { type: "success" })
    } else {
      console.error("第三方登录失败:", res)
      message("第三方登录失败，请重试", { type: "error" })
      setTimeout(() => {
        router.push("/login")
      }, 2000)
    }
  } catch (error) {
    console.error("处理OAuth回调出错:", error)
    message("登录过程中出现错误，请重试", { type: "error" })
    setTimeout(() => {
      router.push("/login")
    }, 2000)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 组件挂载时处理OAuth回调
  handleOAuthCallback()
})
</script>

<template>
  <div class="oauth-callback-container">
    <div class="oauth-callback-card">
      <h2 class="callback-title">{{ provider }} 登录</h2>
      <div v-if="loading" class="callback-loading">
        <el-progress
          type="circle"
          :percentage="100"
          status="success"
          indeterminate
        />
        <p class="loading-text">正在完成登录，请稍候...</p>
      </div>
      <div v-else class="callback-success">
        <el-result
          icon="success"
          title="登录成功"
          sub-title="正在为您跳转到首页..."
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.oauth-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.oauth-callback-card {
  width: 400px;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  text-align: center;
}

.callback-title {
  margin-bottom: 30px;
  font-size: 24px;
  color: #303133;
}

.callback-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-text {
  color: #606266;
  font-size: 16px;
}

.callback-success {
  margin-top: 20px;
}
</style>
