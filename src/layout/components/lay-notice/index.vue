<script setup lang="ts">
import { useI18n } from "vue-i18n"
import { ref, computed, onMounted } from "vue"
import { noticesData } from "./data"
import NoticeList from "./components/NoticeList.vue"
import BellIcon from "~icons/ep/bell"
import {
  getMessageCountApi,
  getMessageApi,
  getMessageMarkAllAsReadApi,
  deleteAllReadMessageApi,
  deleteMessageApi
} from "@/api/message"
import { title } from "process"
import { formatISODate } from "@/utils/date"
import { ElMessageBox, ElMessage } from "element-plus"

const { t } = useI18n()
const noticesNum = ref(0)
// 使用ref存储原始数据
const rawNoticeData = ref([...noticesData])
// 使用计算属性创建响应式的消息列表
const notices = computed(() => {
  return rawNoticeData.value
})

const activeKey = ref(noticesData[0]?.key)

//notices.value.map(v => (noticesNum.value += v.list.length));

const getLabel = computed(
  () => item =>
    t(item.name) + (item.list.length > 0 ? `(${item.list.length})` : "")
)

const getMessageCount = async () => {
  let res = await getMessageCountApi()
  noticesNum.value = res.data.unreadCount
}

const getMessage = async () => {
  let res = await getMessageApi()
  // 获取到数组里通知key为2的对象引用，直接修改targetNotice就能影响noticesData对应的数组
  const targetNotice = rawNoticeData.value.find(notice => notice.key === "2")
  if (targetNotice) {
    targetNotice.list = res.data.list.map(v => {
      return {
        id: v.id,
        avatar: v.avatar,
        title: v.title,
        description: v.content,
        datetime: formatISODate(v.createdTime),
        senderName: v.senderName
      }
    })
  }
}

const getMessageMarkAllAsRead = async () => {
  if (noticesNum.value == 0) {
    return
  }
  let res = await getMessageMarkAllAsReadApi()
  if (res.success) {
    noticesNum.value = 0
  }
}

onMounted(() => {
  getMessageCount()
  getMessage()
})

const clearReadNotice = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      t("你确定要清空消息吗？"),
      t("警告"),
      {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        type: "warning"
      }
    )
    if (confirmResult === "confirm") {
      let res = await deleteAllReadMessageApi()
      if (res.success) {
        ElMessage.success(t("清空成功"))

        // 清空所有通知数据
        const targetNotice = rawNoticeData.value.find(
          notice => notice.key === "2"
        )
        if (targetNotice) {
          targetNotice.list = []
        }

        // 更新未读消息数量
        noticesNum.value = 0
      } else {
        ElMessage.error(t("清空失败"))
      }
    }
  } catch (error) {
    // User cancelled the operation
    console.log("Operation cancelled", error)
  }
}

// Handle refresh from NoticeList (which receives it from NoticeItem)
const handleRefresh = () => {
  getMessage()
  getMessageCount()
}

// 处理删除单个消息
const handleDeleteMessage = async messageId => {
  try {
    const res = await deleteMessageApi(messageId)
    if (res.success) {
      ElMessage.success(t("删除成功"))

      // 更新本地数据，从通知列表中移除被删除的消息
      const targetNotice = rawNoticeData.value.find(
        notice => notice.key === "2"
      )
      if (targetNotice && targetNotice.list) {
        // 使用响应式方法更新数组
        targetNotice.list = targetNotice.list.filter(
          item => item.id !== messageId
        )

        // 如果删除后列表为空，更新通知计数
        if (targetNotice.list.length === 0) {
          noticesNum.value = 0
        } else {
          // 更新未读消息计数
          getMessageCount()
        }
      }
    } else {
      ElMessage.error(t("删除失败"))
    }
  } catch (error) {
    console.error("删除消息失败", error)
    ElMessage.error(t("删除失败"))
  }
}
</script>

<template>
  <el-dropdown trigger="click" placement="bottom-end">
    <span
      :class="[
        'dropdown-badge',
        'navbar-bg-hover',
        'select-none',
        Number(noticesNum) !== 0 && 'mr-[10px]'
      ]"
      @click="getMessageMarkAllAsRead"
    >
      <!--标记-->
      <el-badge :value="Number(noticesNum) === 0 ? '' : noticesNum" :max="99">
        <span class="header-notice-icon">
          <IconifyIconOffline :icon="BellIcon" />
        </span>
      </el-badge>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-tabs
          v-model="activeKey"
          :stretch="true"
          class="dropdown-tabs"
          :style="{ width: notices.length === 0 ? '200px' : '330px' }"
        >
          <el-empty
            v-if="notices.length === 0"
            :description="t('status.pureNoMessage')"
            :image-size="60"
          />
          <span v-else>
            <template v-for="item in notices" :key="item.key">
              <el-tab-pane :label="getLabel(item)" :name="`${item.key}`">
                <el-scrollbar max-height="330px">
                  <div class="noticeList-container">
                    <NoticeList
                      :list="item.list"
                      :emptyText="item.emptyText"
                      @clearReadNotice="clearReadNotice"
                      @refresh="handleRefresh"
                      @deleteMessage="handleDeleteMessage"
                    />
                  </div>
                </el-scrollbar>
              </el-tab-pane>
            </template>
          </span>
        </el-tabs>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style lang="scss" scoped>
.dropdown-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 48px;
  cursor: pointer;

  .header-notice-icon {
    font-size: 18px;
  }
}

.dropdown-tabs {
  .noticeList-container {
    padding: 15px 24px 0;
  }

  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap)::after {
    height: 1px;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 36px;
  }
}
</style>
