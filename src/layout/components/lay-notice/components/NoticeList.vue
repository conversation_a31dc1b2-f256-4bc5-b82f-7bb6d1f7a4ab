<script setup lang="ts">
import { PropType } from "vue"
import { ListItem } from "../data"
import NoticeItem from "./NoticeItem.vue"
import { transformI18n } from "@/plugins/i18n"

defineProps({
  list: {
    type: Array as PropType<Array<ListItem>>,
    default: () => []
  },
  emptyText: {
    type: String,
    default: ""
  }
})

const emits = defineEmits(["clearReadNotice", "refresh", "deleteMessage"])

// Handle refresh from individual NoticeItem
const handleItemRefresh = () => {
  emits("refresh")
}

// Handle delete message from NoticeItem
const handleDeleteMessage = (messageId: number) => {
  emits("deleteMessage", messageId)
}
</script>

<template>
  <div v-if="list.length">
    <NoticeItem
      v-for="(item, index) in list"
      :key="index"
      :noticeItem="item"
      @refresh="handleItemRefresh"
      @deleteMessage="handleDeleteMessage"
    />
  </div>
  <el-empty v-else :description="transformI18n(emptyText)" />
</template>
