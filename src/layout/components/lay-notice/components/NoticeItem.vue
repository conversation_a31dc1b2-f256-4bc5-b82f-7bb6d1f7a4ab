<script setup lang="ts">
import { ListItem } from "../data"
import { ref, PropType, nextTick } from "vue"
import { useNav } from "@/layout/hooks/useNav"
import { deviceDetection } from "@pureadmin/utils"
import MingcuteDelete3Line from "~icons/mingcute/delete-3-line"
import { deleteMessageApi } from "@/api/message"
import { ElMessageBox, ElMessage } from "element-plus"
import { useI18n } from "vue-i18n"
import MaterialSymbolsLightDeleteOutlineRounded from "~icons/material-symbols-light/delete-outline-rounded"
const { t } = useI18n()
const props = defineProps({
  noticeItem: {
    type: Object as PropType<ListItem>,
    default: () => {}
  }
})

const titleRef = ref(null)
const titleTooltip = ref(false)
const descriptionRef = ref(null)
const descriptionTooltip = ref(false)
const { tooltipEffect } = useNav()
const isMobile = deviceDetection()

function hoverTitle() {
  nextTick(() => {
    titleRef.value?.scrollWidth > titleRef.value?.clientWidth
      ? (titleTooltip.value = true)
      : (titleTooltip.value = false)
  })
}

function hoverDescription(event, description) {
  // currentWidth 为文本在页面中所占的宽度，创建标签，加入到页面，获取currentWidth ,最后在移除
  const tempTag = document.createElement("span")
  tempTag.innerText = description
  tempTag.className = "getDescriptionWidth"
  document.querySelector("body").appendChild(tempTag)
  const currentWidth = (
    document.querySelector(".getDescriptionWidth") as HTMLSpanElement
  ).offsetWidth
  document.querySelector(".getDescriptionWidth").remove()

  // cellWidth为容器的宽度
  const cellWidth = event.target.offsetWidth

  // 当文本宽度大于容器宽度两倍时，代表文本显示超过两行
  currentWidth > 2 * cellWidth
    ? (descriptionTooltip.value = true)
    : (descriptionTooltip.value = false)
}

async function deleteNotice() {
  try {
    await ElMessageBox.confirm(t("确定要删除这条消息吗？"), t("提示"), {
      confirmButtonText: t("确认"),
      cancelButtonText: t("取消"),
      type: "warning"
    })

    // 不在这里直接调用API，而是将消息ID传递给父组件处理
    emits("deleteMessage", props.noticeItem.id)
  } catch (error) {
    // 用户取消操作
    console.log("Operation cancelled", error)
  }
}

const emits = defineEmits(["refresh", "deleteMessage"])
</script>

<template>
  <div
    class="notice-container border-b-[1px] border-solid border-[#f0f0f0] dark:border-[#303030]"
  >
    <!-- 左侧部分：发送者名称和头像 -->
    <div class="notice-left-part">
      <div class="sender-name">{{ noticeItem.senderName }}</div>
      <el-avatar
        v-if="noticeItem.avatar"
        :size="30"
        :src="noticeItem.avatar"
        class="notice-container-avatar"
      />
    </div>

    <!-- 右侧部分：标题、描述、时间等 -->
    <div class="notice-container-text">
      <div class="notice-text-title text-[#000000d9] dark:text-white">
        <el-tooltip
          popper-class="notice-title-popper"
          :effect="tooltipEffect"
          :disabled="!titleTooltip"
          :content="noticeItem.title"
          placement="top-start"
          :enterable="!isMobile"
        >
          <div
            ref="titleRef"
            class="notice-title-content"
            @mouseover="hoverTitle"
          >
            {{ noticeItem.title }}
          </div>
        </el-tooltip>
        <el-tag
          v-if="noticeItem?.extra"
          :type="noticeItem?.status"
          size="small"
          class="notice-title-extra"
        >
          {{ noticeItem?.extra }}
        </el-tag>
      </div>

      <el-tooltip
        popper-class="notice-title-popper"
        :effect="tooltipEffect"
        :disabled="!descriptionTooltip"
        :content="noticeItem.description"
        placement="top-start"
      >
        <div class="items-center">
          <div
            ref="descriptionRef"
            class="notice-text-description"
            @mouseover="hoverDescription($event, noticeItem.description)"
          >
            {{ noticeItem.description }}
          </div>
          <MingcuteDelete3Line
            class="cursor-pointer"
            style="width: 16px; height: 16px"
            @click="deleteNotice"
          ></MingcuteDelete3Line>
        </div>
      </el-tooltip>
      <div class="notice-text-datetime text-[#00000073] dark:text-white">
        {{ noticeItem.datetime }}
      </div>
    </div>
  </div>
</template>

<style>
/* Tooltip 样式保持不变 */
.notice-title-popper {
  max-width: 238px;
}

/* 用于计算宽度的临时标签，最好设置 display: inline-block 或其他确保宽度的样式 */
.getDescriptionWidth {
  position: absolute;
  display: inline-block;
  visibility: hidden;
  white-space: nowrap; /* 假设原始描述不是强制换行的 */
}
</style>
<style lang="scss" scoped>
.notice-container {
  display: flex;
  align-items: flex-start; // 左侧和右侧顶部对齐
  padding: 12px 0;
  // border-bottom 样式已通过内联 tailwind 类设置

  .notice-left-part {
    display: flex;
    flex-shrink: 0; // 防止此部分被压缩
    flex-direction: column; // 名字和头像垂直排列
    align-items: center; // 名字和头像水平居中
    margin-right: 16px; // 在左侧部分和右侧文本之间添加间距

    .sender-name {
      margin-bottom: 4px; // 在名字和头像之间添加间距
      font-size: 12px; // 设置合适的字体大小
      color: #888; // 设置合适的颜色 (可调整)
      text-align: center; // 名字文本居中
      // white-space: nowrap; // 如果名字可能很长，考虑是否需要不换行
      // overflow: hidden;
      // text-overflow: ellipsis;
      // max-width: 50px; // 限制最大宽度，根据头像大小调整
    }

    .notice-container-avatar {
      // 移除了 margin-right，间距由父元素 notice-left-part 控制
      background: #fff;
    }
  }

  .notice-container-text {
    display: flex;
    flex: 1; // 占据剩余空间
    flex-direction: column;
    justify-content: space-between; // 内部元素垂直分布

    .notice-text-title {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.5715;
      cursor: pointer;

      .notice-title-content {
        flex: 1;
        min-width: 0; // 防止在 flex 布局中内容溢出父容器
        // width: 200px; // 宽度由 flex: 1 决定，不再需要固定宽度
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .notice-title-extra {
        flex-shrink: 0; // 防止 tag 被压缩
        margin-top: -1.5px; // 微调垂直位置
        // float: right; // 在 flex 布局中不需要 float
        margin-left: 8px; // 与标题内容保持一点距离
        font-weight: 400;
      }
    }

    .items-center {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .cursor-pointer {
      justify-self: center;
      margin-right: 23px;
    }
    .notice-text-datetime {
      font-size: 12px;
      line-height: 1.5715;
    }

    .notice-text-description {
      display: -webkit-box;
      margin-bottom: 4px; // 与下面的时间增加一点间距
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      color: #666; // 给描述添加一个颜色，使其与标题和时间区分
      -webkit-box-orient: vertical;
    }
  }
}

.dark {
  .sender-name {
    color: #bbb; // 暗黑模式下的名字颜色
  }

  .notice-text-description {
    color: #aaa; // 暗黑模式下的描述颜色
  }
}
</style>
