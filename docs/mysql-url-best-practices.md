# MySQL URL 最佳实践配置指南

## 🎯 概述
本文档提供了不同环境下MySQL JDBC URL的最佳实践配置。

## 📋 完整参数说明

### 基础连接参数
| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `useUnicode` | 启用Unicode支持 | `true` |
| `characterEncoding` | 字符编码 | `utf8mb4` (支持emoji) |
| `serverTimezone` | 服务器时区 | `Asia/Shanghai` |

### 安全参数
| 参数 | 说明 | 开发环境 | 生产环境 |
|------|------|----------|----------|
| `useSSL` | 启用SSL加密 | `false` | `true` |
| `allowPublicKeyRetrieval` | 允许公钥检索 | `true` | `true` |
| `verifyServerCertificate` | 验证服务器证书 | `false` | `true` |

### 连接可靠性参数
| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `autoReconnect` | 自动重连 | `true` |
| `failOverReadOnly` | 故障转移只读 | `false` |
| `maxReconnects` | 最大重连次数 | `3` |
| `connectTimeout` | 连接超时(毫秒) | `30000` |
| `socketTimeout` | 读取超时(毫秒) | `60000` |

### 性能优化参数
| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `cachePrepStmts` | 缓存预编译语句 | `true` |
| `useServerPrepStmts` | 使用服务器预编译 | `true` |
| `prepStmtCacheSize` | 预编译缓存大小 | `250` |
| `prepStmtCacheSqlLimit` | SQL缓存限制 | `2048` |
| `rewriteBatchedStatements` | 重写批处理语句 | `true` |

## 🌍 环境配置示例

### 开发环境 (当前配置)
```yaml
SPRING_DATASOURCE_PRIMARY_URL: >
  *************************?
  useUnicode=true&
  characterEncoding=utf8mb4&
  serverTimezone=Asia/Shanghai&
  useSSL=true&
  allowPublicKeyRetrieval=true&
  autoReconnect=true&
  connectTimeout=30000&
  socketTimeout=60000&
  cachePrepStmts=true&
  useServerPrepStmts=true&
  prepStmtCacheSize=250&
  rewriteBatchedStatements=true
```

### 生产环境 (高安全性)
```yaml
SPRING_DATASOURCE_PRIMARY_URL: >
  ************************************?
  useUnicode=true&
  characterEncoding=utf8mb4&
  serverTimezone=Asia/Shanghai&
  useSSL=true&
  verifyServerCertificate=true&
  allowPublicKeyRetrieval=false&
  autoReconnect=true&
  maxReconnects=3&
  connectTimeout=30000&
  socketTimeout=60000&
  cachePrepStmts=true&
  useServerPrepStmts=true&
  prepStmtCacheSize=500&
  rewriteBatchedStatements=true
```

## ⚠️ 常见问题和解决方案

### 1. SSL连接问题
**问题**: SSL握手失败
**解决**: 
- 开发环境: 可以使用 `useSSL=false`
- 生产环境: 配置正确的SSL证书

### 2. 时区问题
**问题**: 时间数据不一致
**解决**: 设置 `serverTimezone=Asia/Shanghai`

### 3. 字符编码问题
**问题**: 中文或emoji显示乱码
**解决**: 使用 `characterEncoding=utf8mb4` (推荐，支持完整UTF-8包括emoji)

## 🔧 配置验证
```bash
# 检查数据库连接
curl -f http://localhost:8080/actuator/health

# 检查数据库字符集
docker exec myapp-db-1 mysql -u root -p -e "SHOW VARIABLES LIKE 'character_set%';"
```
